import React, { useMemo, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import {
    Box,
    IconButton,
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
} from '@majoo-ui/react';
import { EllipsisHorizontalOutline } from '@majoo-ui/icons';
import { get } from 'lodash'
import * as marketplaceAPI from '~/data/marketplace';
import { catchError } from '~/utils/helper';
import warningImage from '../../../../assets/images/icon-warning.png';
import ModalPopup from '../../modalpopup/v.2/Container';

import {
    STATUS_INTEGRASI, ACTION_BY_STATUS, ACTION_TABLE, ACTION_TRIGER, PROVIDER_ID,
} from '../utils/enums';
import '../styles/action_options.less';
import { accountType } from '../../../config/enum';
import userUtil from '../../../utils/user.util';
import { getTextInfoLimitedAccount, providerName } from '../utils/utils';
import { Trans } from 'react-i18next';

const ActionOptions = (props) => {
    const {
        value: {
            status,
            showProgress,
            hideProgress,
            noSubmission,
            idOutlet,
            refetch,
            note,
            mid,
            setModalConfirmation,
        },
        translation,
        provider,
        router,
        showPopupSubmission,
        addNotification,
    } = props;
    const { location: { pathname } } = router;
    const original = get(props, 'row.original') ? get(props, 'row.original') : get(props, 'original');
    const [open, setOpen] = useState(false);
    
    const navigate = useNavigate();
    const listAction = useMemo(() => {
        if (ACTION_BY_STATUS[status]) {
            let tempList = ACTION_BY_STATUS[status].map(id => ACTION_TABLE(translation)[id]);
            if (provider === PROVIDER_ID.GOBIZ && status === STATUS_INTEGRASI.INTEGRATED) tempList = tempList.slice(0, 1);
            return tempList;
        }
        return [];
    }, [status, provider]);
    const limitedIntegrationContent = useMemo(() => getTextInfoLimitedAccount(translation, provider), [translation]);

    const modalConfirmation = useRef();
    const pembatasanIntegration = useRef();
    const CancellationSubmissionModal = useRef();
    const rejectReason = useRef();

    const needToPurchaseAddOn = (data) => {
        const accountTypes = [accountType.TRIAL, accountType.ENTERPRISE, accountType.PRIMEPLUS];
        const currentAccountType = userUtil.getLocalConfigByKey('accountType');

        const found = accountTypes.some(x => (x === currentAccountType));

        if (!found) {
            const { status: statusData, status_addon: statusAddon } = data;
            return (statusAddon && statusData !== STATUS_INTEGRASI.SUSPEND);
        }
        return found;
    };

    const handleSubmissionGofood = async () => {
        const payload = {
            provider_id: provider,
            outlet_id: Number(idOutlet),
        };
        const baseUrl = `/${providerName(provider).toLowerCase()}/integration/${idOutlet}`;

        showProgress();
        try {
            const res = await marketplaceAPI.orderOnlineIntegration(payload);

            if (!res.status) router.push(baseUrl);
        } catch (error) {
            addNotification({
                title: translation('translation:toast.error'),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const handleGrabSubmission = async () => {
        const payload = {
            providerId: provider,
            outletId: Number(idOutlet),
        };
        showProgress();
        try {
            const res = await marketplaceAPI.grabSelfSubmission(payload);
            if (res.mid) navigate(`${pathname}/pengaturan-grab-merchant/${idOutlet}?mid=${res.mid}`);
            if (res.auth_url && setModalConfirmation) {
                setModalConfirmation(prev => ({
                    ...prev,
                    open: true,
                    title: translation('translation:label.confirmation', 'Konfirmasi'),
                    description: (
                        <Box
                            css={{
                                '& > ol': {
                                    paddingLeft: 20,
                                    '& > li': { 
                                        fontWeight: 700,
                                        '& > p': {
                                            fontWeight: 400
                                        }
                                    }
                                },
                                color: '$textPrimary'
                            }}
                            dangerouslySetInnerHTML={{__html: translation("integration.confirmation.desc2") }}
                        />
                    ),
                    confirmLabel: translation('translation:label.continue', 'Ya, Lanjutkan'),
                    onConfirm: () => window.open(res.auth_url, '_blank'),
                }));
            }
        } catch (error) {
            addNotification({
                title: translation('translation:toast.error'),
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const openPopup = reff => setTimeout(() => reff.current.showPopup(), 200);

    const handleAction = (type) => {
        const paramMid = mid ? `?mid=${mid}` : '';
        switch (type) {
            case ACTION_TRIGER.SHOW_CONFIRMATION:
                setOpen(false);
                if (!needToPurchaseAddOn(original)) {
                    openPopup(pembatasanIntegration);
                    break;
                }
                if (showPopupSubmission) {
                    openPopup(modalConfirmation);
                } else if (provider === PROVIDER_ID.GOBIZ) {
                    handleSubmissionGofood();
                } else {
                    handleGrabSubmission();
                }
                break;
            case ACTION_TRIGER.SHOW_DETAIL_SUBMISSION:
                navigate(`pengajuan-akun-digital/grab-merchant/edit/${noSubmission}`);
                break;
            case ACTION_TRIGER.SUBMISSION_INTEGRATION:
                setOpen(false);
                if (!needToPurchaseAddOn(original)) {
                    openPopup(pembatasanIntegration);
                    break;
                }
                if (provider === PROVIDER_ID.GOBIZ) {
                    navigate(`${pathname}/integration/${idOutlet}`);
                } else {
                    handleGrabSubmission();
                }
                
                break;
            case ACTION_TRIGER.SHOW_MODAL_REJECT:
                setOpen(false);
                openPopup(rejectReason);
                break;
            case ACTION_TRIGER.FIRST_TIME_SETUP:
                setOpen(false);
                if (!needToPurchaseAddOn(original)) {
                    openPopup(pembatasanIntegration);
                    break;
                }
                if (provider === PROVIDER_ID.GOBIZ) {
                    navigate(`${pathname}/initial-setup/${idOutlet}${paramMid}`);
                    break;
                }
                handleGrabSubmission();
                break;
            case ACTION_TRIGER.SETUP_GRAB:
                if (provider === PROVIDER_ID.GOBIZ) {
                    navigate(`${pathname}/setting/${idOutlet}${paramMid}`);
                    break;
                }
                navigate(`${pathname}/pengaturan-grab-merchant/${idOutlet}${paramMid}`);
                break;
            case ACTION_TRIGER.DISCONNECT_INTEGRATION:
                setOpen(false);
                openPopup(CancellationSubmissionModal);
                break;
            default:
                break;
        }
    };

    const handleModalReject = () => {
        if (status === STATUS_INTEGRASI.REJECTED) {
            handleAction(ACTION_TRIGER.SHOW_DETAIL_SUBMISSION);
        } else {
            handleAction(ACTION_TRIGER.SUBMISSION_INTEGRATION);
        }
    };

    const handlePutuskanIntegration = () => {
        const payload = {
            id_outlet: idOutlet,
            status: STATUS_INTEGRASI.SUSPEND,
            provider_id: provider,
        };

        showProgress();
        marketplaceAPI.putSubmissionStatus(payload)
            .then(() => {
                refetch();
                CancellationSubmissionModal.current.hidePopup();
            })
            .catch((e) => {
                const message = catchError(e);
                addNotification({
                    title: translation('translation:toast.error'),
                    message,
                    level: 'error',
                });
            })
            .finally(() => hideProgress());
    };

    return (
        <Box>
            <DropdownMenu open={open} onOpenChange={setOpen} side="top">
                <DropdownMenuTrigger asChild>
                    <IconButton>
                        <EllipsisHorizontalOutline size={20} color="currentColor" />
                    </IconButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent sideOffset={5} css={{ width: 208 }}>
                    {listAction.map((data, i) => (
                        <DropdownMenuItem
                            key={`action-${i + 1}`}
                            onClick={() => setTimeout(() => handleAction(data.action), 100)}
                            disabled={data.isDisable}
                        >
                            {translation(`buttonAction.${data.key}`, data.name)}
                        </DropdownMenuItem>
                    ))}
                </DropdownMenuContent>
            </DropdownMenu>
            <ModalPopup
                confirmHandle={() => navigate(`${pathname}/integration/${idOutlet}`)}
                onHide={() => navigate('/pengajuan-akun-digital/grab-merchant/create')}
                ref={modalConfirmation}
                confirmText={translation('label.yes', 'Sudah', { ns: 'translation' })}
                cancelText={translation('label.no', 'Belum', { ns: 'translation' })}
                fixedWidth={575}
                loadingText={translation('label.waiting', 'Tunggu...', { ns: 'translation' })}
            >
                <h4 className="title-modal">{translation('integration.confirmation.title', 'Konfirmasi Akun Grab')}</h4>
                <div className="divider" />
                <h5 className="mt-md mb-xl">{translation('integration.confirmation.desc', 'Apakah anda sudah mempunyai akun Grab Merchant?')}</h5>
            </ModalPopup>
            <ModalPopup
                confirmHandle={handleModalReject}
                ref={rejectReason}
                confirmText={status === STATUS_INTEGRASI.REJECTED ? translation('listIntegration.actionButton.edit', 'Edit Dokumen') : translation('listIntegration.actionButton.reSubmit', 'Ajukan Ulang')}
                cancelText={translation('label.cancel', 'Batal', { ns: 'translation' })}
                fixedWidth={575}
                loadingText={translation('label.waiting', 'Tunggu...', { ns: 'translation' })}
            >
                <div className="text-center" style={{ maxWidth: '500px' }}>
                    <div style={{ display: 'flex', justifyContent: 'center' }}>
                        <img src={warningImage} alt="warning-reject" style={{ objectFit: 'cover', width: '64px' }} />
                    </div>
                    <h4 className="title-modal">{translation('listIntegration.actionButton.reason', 'Alasan Ditolak')}</h4>
                    <p className="text">
                        {
                            status === STATUS_INTEGRASI.REJECTED
                                ? translation('reject.document', 'Terdapat dokumen foto anda yang kurang jelas')
                                : translation('reject.account', 'Akun yang anda ajukan tidak ada atau berbeda')
                        }
                    </p>
                    <p className="text">{note}</p>
                </div>
            </ModalPopup>
            <ModalPopup
                confirmHandle={() => navigate('/support/buy?tab=support')}
                ref={pembatasanIntegration}
                confirmText={limitedIntegrationContent.btnText}
                cancelText={translation('label.close', 'Tutup', { ns: 'translation' })}
                loadingText={translation('label.waiting', 'Tunggu...', { ns: 'translation' })}
            >
                <div className="text-center" style={{ maxWidth: '700px' }}>
                    <div style={{ display: 'flex', justifyContent: 'center' }}>
                        <img src={warningImage} alt="warning-reject" style={{ objectFit: 'cover', width: '64px' }} />
                    </div>
                    <h4 className="title-modal">{limitedIntegrationContent.title}</h4>
                    <p className="text">
                        {limitedIntegrationContent.body}
                    </p>
                </div>
            </ModalPopup>
            <ModalPopup
                headerTitle={translation('listIntegration.actionButton.disconnect', 'Putuskan Integrasi')}
                confirmButtonClass="bgOrange"
                confirmText={translation('label.process', 'Proses', { ns: 'translation' })}
                cancelText={translation('label.cancel', 'Batal', { ns: 'translation' })}
                ref={CancellationSubmissionModal}
                confirmHandle={() => handlePutuskanIntegration()}
                fixedWidth={575}
                loadingText={translation('label.waiting', 'Tunggu...', { ns: 'translation' })}
            >
                <div style={{ maxWidth: '500px' }}>
                    <p className="text">
                        {translation('disconnect.desc', 'Memutuskan integrasi dengan GrabFood akan menghapus semua relasi outlet dengan akun GrabFood yang sudah terintegrasi sebelumnya. Proses ini membutuhkan waktu 2 - 7 x 24 jam hingga proses pemutusan selesai sepenuhnya.', { provider: providerName(provider) })}
                    </p>
                </div>
            </ModalPopup>
        </Box>
    );
};

ActionOptions.propTypes = {
    value: PropTypes.shape({
        name: PropTypes.string,
        status: PropTypes.number,
        isLast: PropTypes.bool,
        noSubmission: PropTypes.string,
        note: PropTypes.string,
        idOutlet: PropTypes.string,
        isAdvanceOutlet: PropTypes.bool,
        showProgress: PropTypes.func,
        hideProgress: PropTypes.func,
        action: PropTypes.func,
        isDisable: PropTypes.bool,
        refetch: PropTypes.func,
        mid: PropTypes.string,
        setModalConfirmation: PropTypes.func
    }),
    addNotification: PropTypes.func,
    router: PropTypes.shape({
        location: PropTypes.shape({
            pathname: PropTypes.string,
        }),
        push: PropTypes.func,
    }),
    showPopupSubmission: PropTypes.bool,
    original: PropTypes.shape({}),
    provider: PropTypes.number.isRequired,
    translation: PropTypes.func,
};

ActionOptions.defaultProps = {
    value: {
        status: STATUS_INTEGRASI.WAITING_INTEGRATION,
        isLast: false,
        idOutlet: '',
        noSubmission: '',
        note: '',
        isAdvanceOutlet: false,
        mid: '',
        
    },
    addNotification: () => { },
    router: {},
    showPopupSubmission: false,
    original: {},
    translation: () => { },
};

export default ActionOptions;
