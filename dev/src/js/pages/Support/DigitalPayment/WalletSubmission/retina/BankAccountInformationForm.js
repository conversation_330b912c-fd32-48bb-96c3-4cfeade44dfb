import React, { useEffect, useState } from 'react';

import {
    Box, Paper, Heading, Paragraph, ToastContext, FormGroup, FormLabel, FormHelper, InputSelect, Flex, Button, Text, Image,
} from '@majoo-ui/react';
import { useTranslation } from 'react-i18next';
import { isMobile } from '~/config/config';
import { PlusOutline } from '@majoo-ui/icons';
import { SideInformation } from './utils';
import {
    BANK_ACCOUNT_STATUS, BANK_STATUS_TYPE,
} from './enum';
import { catchError } from '../../../../../utils/helper';
import {
    changeStatusActiveRekening, createSubmissionProfile, getListSubmissionProfile, updateSubmissionProfile,
} from '../../../../../data/users';
import AddBankAccountForm from '../../../../Settings/UserProfile/BankAccount/retina/AddBankAccountForm';
import { getBank } from '../../../../../data/outlets';
import perngajuanWallet from '../../../../../../assets/locales/id/Layanan/pengajuanWallet.json';
import { useBankAccountInformationForm } from './hooks/useBankAccountInformationForm';
import StatusAccount from './StatusAccount';
import RekeningNotFound from '../../asset/rekening_not_found.png';

const renderOption = val => (
    <Flex direction="column" gap={2}>
        <Box>
            <Text css={{ fontWeight: 600, fontSize: 14 }}>
                {val.bank_name}
                {' '}
                -
                {' '}
                {val.account_no}
            </Text>
        </Box>
        <Box>
            <Text css={{ fontWeight: 400, fontSize: 14 }}>
                {val.account_holder}
                {' '}
                -
                {' '}
                {val.outlet_name}
            </Text>
        </Box>
    </Flex>
);

const BankAccountInformationForm = (props) => {
    const {
        hookForm, hideProgress, showProgress, listCabang, detail, isDisabled, businessProfile, filterBranch, idCabang,
    } = props;
    const {
        getValues, formState: { errors }, setValue, clearErrors, watch,
    } = hookForm;
    const {
        isLoading,
        setIsLoading,
        isIntegratedWebstore,
        setIsIntegratedWebstore,
        isIntegratedWallet,
        setIsIntegratedWallet,
    } = useBankAccountInformationForm();
    const [tableData, setTableData] = useState([]);
    const [outletOptions, setOutletOptions] = useState([]);
    const [bankOptions, setBankOptions] = useState([]);
    const [totalData, setTotalData] = useState(0);
    const [openBankAccountForm, setOpenBankAccountForm] = useState(false);
    const [tableQuery, setTableQuery] = useState({
        limit: 10000,
        page: 0,
        search: '',
        id_outlet: getValues('outlets'),
        order: null,
        sort: null,
        type: 'rekening',
    });
    const [accountDetail, setAccountDetail] = useState({});
    const { addToast } = React.useContext(ToastContext);
    const { t } = useTranslation(['Layanan/pengajuanWallet', 'translation']);
    const { t: tBank } = useTranslation(['Pengaturan/AkunProfil/bankInformation', 'translation']);
    const { createWallet } = perngajuanWallet;

    const fetchData = async (param, isFromButton = false) => {
        setIsLoading(true);
        const {
            page, search, id_outlet, order, sort, limit, type,
        } = param;
        const payload = {
            type,
            limit,
            page: page + 1,
            ...(search && {
                search,
            }),
            ...(id_outlet && {
                id_outlet,
            }),
            ...(order && {
                order,
            }),
            ...(sort && {
                sort,
            }),
        };
        try {
            const {
                data, status, meta, msg,
            } = await getListSubmissionProfile(payload);
            if (!status) throw new Error(msg);
            if (isFromButton) {
                addToast({
                    id: 'refresh-bank',
                    title: t('toast.success', 'Berhasil!', { ns: 'translation' }),
                    variant: 'success',
                    description: t('createWallet.stepFour.toast.successUpdate', 'Informasi rekening berhasil diperbarui'),
                    preventDuplicate: true,
                });
            }
            setTableData(data.map(x => ({
                value: x.id,
                name: x.bank_name,
                isDisabled: x.status_id === 112,
                render: () => renderOption(x),
                ...x,
            })));
            setTotalData(meta.total);
        } catch (e) {
            addToast({
                title: t('toast.failGotData', 'Gagal mendapatkan data', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            hideProgress();
            setIsLoading(false);
        }
    };

    const fetchBank = async () => {
        const res = await getBank();
        if (res.status) {
            setBankOptions(res.data.map(val => ({ value: val.id_bank, name: val.bank_name })));
        }
    };

    const onSubmit = async (values) => {
        const { payload } = values;
        const newPayload = {
            ...payload,
            status: payload.status ? 'active' : 'deactive',
        };
        delete newPayload.id;
        showProgress();

        try {
            const res = await createSubmissionProfile(newPayload);
            if (res.status) {
                addToast({ title: t('toast.success', { ns: 'translation' }), variant: 'success', description: t('createWallet.stepFour.toast.successAddAcc') });
            } else {
                throw new Error();
            }
        } catch (e) {
            addToast({ title: t('toast.error', { ns: 'translation' }), variant: 'failed', description: t('createWallet.stepFour.toast.failAddAcc') });
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            setOpenBankAccountForm(false);
            fetchData({ ...tableQuery });
            hideProgress();
        }
    };

    useEffect(() => {
        fetchData({ ...tableQuery });
    }, [tableQuery]);

    useEffect(() => {
        setOutletOptions(listCabang.map(o => ({ name: o.cabang_name, id: o.id_cabang })));
    }, [listCabang]);

    useEffect(() => {
        showProgress();
        fetchBank();
    }, []);

    const translate = key => t(`createWallet.stepFour.table.${key}`);

    const accountSelected = watch('merchant_profile_id') ? tableData.find(x => x.value === watch('merchant_profile_id')) : null;

    return (
        <React.Fragment>
            <Box
                css={{
                    display: 'grid',
                    gridTemplateColumns: '1fr',
                    gap: '$cozy',
                }}
            >
                <SideInformation detail={detail} />
                <Box css={{ display: 'grid', gridTemplateColumns: '1fr', gap: '$comfortable' }}>
                    <Paper responsive css={{ padding: '$cozy', display: 'grid', gap: '$cozy' }}>
                        <Box css={{ '@md': { marginBottom: '$spacing-03' } }}>
                            <Heading
                                as="h3"
                                heading={{
                                    '@initial': 'sectionSubTitle',
                                    '@md': 'pageTitle',
                                }}
                            >
                                {t('createWallet.stepFour.title', 'Informasi Rekening')}
                            </Heading>
                        </Box>
                        {tableData.length === 0 ? (
                            <FormGroup responsive="input">
                                <FormLabel htmlFor="npwp_number" variant="required">{t('createWallet.stepFour.subtitle', 'Rekening Bank')}</FormLabel>
                                <Flex gap={4} direction="column" align="center" justify="center">
                                    <Image
                                        src={RekeningNotFound}
                                        alt="Rekening Tidak Ditemukan"
                                        width={200}
                                        height={200}
                                    />
                                    <Text color="primary" css={{ fontSize: 14, fontWeight: 400, textAlign: 'center' }}>
                                        {t('createWallet.stepFour.description.line1', 'Rekening tidak ditemukan,')}
                                        {' '}
                                        <br />
                                        {t('createWallet.stepFour.description.line2', 'silakan tambahkan rekening bank terlebih dahulu')}
                                    </Text>
                                    <Button
                                        type="button"
                                        onClick={() => setOpenBankAccountForm(true)}
                                        size="sm"
                                        leftIcon={<PlusOutline color="currentColor" />}
                                        css={{
                                            display: 'none',
                                            '@md': { display: 'inline-flex' },
                                            width: 300,
                                        }}
                                        disabled={isDisabled}
                                    >
                                        {t('createWallet.stepFour.btnAddRek', 'Tambah Rekening Bank')}
                                    </Button>
                                </Flex>
                                {errors.merchant_profile_id && (
                                    <FormHelper
                                        error
                                    >
                                        {errors.merchant_profile_id.message}
                                    </FormHelper>
                                )}
                            </FormGroup>
                        ) : (
                            <FormGroup responsive="input">
                                <FormLabel htmlFor="npwp_number" variant="required">{t('createWallet.stepFour.subtitle', 'Rekening Bank')}</FormLabel>
                                <Flex gap={6}>
                                    <Flex gap={2} direction="column">
                                        <InputSelect
                                            hideArrowicon
                                            id="merchant_profile_id"
                                            placeholder={`${t('placeholder.select', 'Contoh:', { ns: 'translation' })}`}
                                            search
                                            option={tableData}
                                            value={tableData.find(x => x.value === getValues('merchant_profile_id'))}
                                            onChange={(x) => {
                                                setValue('merchant_profile_id', x.value);
                                                clearErrors('merchant_profile_id');
                                            }}
                                            isInvalid={!!errors.merchant_profile_id}
                                            disabled={isDisabled}
                                        />
                                        <Text css={{ fontSize: 12, fontWeight: 400 }}>
                                            {t('createWallet.stepFour.description.main', 'Pilih nomor rekening yang akan digunakan untuk pendaftaran pembayaran e-Wallet menggunakan rekening Bank BRI, Mandiri, atau BCA')}
                                        </Text>
                                    </Flex>
                                    <Button
                                        type="button"
                                        onClick={() => setOpenBankAccountForm(true)}
                                        size="sm"
                                        leftIcon={<PlusOutline color="currentColor" />}
                                        css={{
                                            display: 'none',
                                            '@md': { display: 'inline-flex' },
                                            width: 300,
                                        }}
                                        disabled={isDisabled}
                                    >
                                        {t('createWallet.stepFour.btnAddRek', 'Tambah Rekening Bank')}
                                    </Button>
                                </Flex>
                                {errors.merchant_profile_id && (
                                    <FormHelper
                                        error
                                    >
                                        {errors.merchant_profile_id.message}
                                    </FormHelper>
                                )}

                                {accountSelected ? (
                                    <Box css={{ padding: 16 }}>
                                        <Flex direction="column" gap={2}>
                                            <Flex gap={9}>
                                                <Box css={{ width: 120, flexShrink: 0 }}>
                                                    <Text color="primary" css={{ fontWeight: 600 }}>
                                                        {translate('namaBank', 'Nama Bank')}
                                                    </Text>
                                                </Box>
                                                <Box>
                                                    <Text color="primary">
                                                        :
                                                        {' '}
                                                        {accountSelected.name}
                                                    </Text>
                                                </Box>
                                            </Flex>

                                            <Flex gap={9}>
                                                <Box css={{ width: 120, flexShrink: 0 }}>
                                                    <Text color="primary" css={{ fontWeight: 600 }}>
                                                        {translate('norek', 'No Rekening')}
                                                    </Text>
                                                </Box>
                                                <Box>
                                                    <Text color="primary">
                                                        :
                                                        {' '}
                                                        {accountSelected.account_no}
                                                    </Text>
                                                </Box>
                                            </Flex>

                                            <Flex gap={9}>
                                                <Box css={{ width: 120, flexShrink: 0 }}>
                                                    <Text color="primary" css={{ fontWeight: 600 }}>
                                                        {translate('holder2', 'Pemilik')}
                                                    </Text>
                                                </Box>
                                                <Box>
                                                    <Text color="primary">
                                                        :
                                                        {' '}
                                                        {accountSelected.account_holder}
                                                    </Text>
                                                </Box>
                                            </Flex>

                                            <Flex gap={9}>
                                                <Box css={{ width: 120, flexShrink: 0 }}>
                                                    <Text color="primary" css={{ fontWeight: 600 }}>
                                                        Outlet
                                                    </Text>
                                                </Box>
                                                <Box>
                                                    <Text color="primary">
                                                        :
                                                        {' '}
                                                        {accountSelected.outlet_name}
                                                    </Text>
                                                </Box>
                                            </Flex>

                                            <Flex gap={9}>
                                                <Box css={{ width: 120, flexShrink: 0 }}>
                                                    <Text color="primary" css={{ fontWeight: 600 }}>
                                                        Status
                                                    </Text>
                                                </Box>
                                                <Flex gap={2}>
                                                    :
                                                    {' '}
                                                    <StatusAccount value={accountSelected.status_id} translate={translate} />
                                                </Flex>
                                            </Flex>
                                        </Flex>
                                    </Box>
                                ) : null}
                            </FormGroup>
                        )}
                    </Paper>
                </Box>
            </Box>

            {openBankAccountForm && (
                <AddBankAccountForm
                    isMobile={isMobile.matches}
                    open={openBankAccountForm}
                    onOpenChange={setOpenBankAccountForm}
                    key={`${accountDetail.id}-${openBankAccountForm}`}
                    onSubmit={payload => onSubmit(payload)}
                    defaultOutlet={String(tableQuery.id_outlet)}
                    outletOptions={outletOptions}
                    bankOptions={bankOptions}
                    detail={{}}
                    setDetail={() => { }}
                    type="1"
                    selectedBank=""
                    selectedOutletEdit={[]}
                    setSelectedOutletEdit={() => { }}
                    selectedMarketplaceEdit={[]}
                    setSelectedMarketplaceEdit={() => { }}
                    marketplaceOptions={[]}
                    ewalletOptions={[]}
                    accountOptions=""
                    fetchData={fetchData}
                    isLoading={isLoading}
                    openInfoModal={() => { }}
                    isIntegratedWebstore={isIntegratedWebstore}
                    setIsIntegratedWebstore={setIsIntegratedWebstore}
                    isIntegratedWallet={isIntegratedWallet}
                    setIsIntegratedWallet={setIsIntegratedWallet}
                    businessProfile={businessProfile}
                    showProgress={showProgress}
                    hideProgress={hideProgress}
                    t={tBank}
                    outletId={filterBranch || idCabang}
                />
            )}
        </React.Fragment>
    );
};

export default BankAccountInformationForm;
