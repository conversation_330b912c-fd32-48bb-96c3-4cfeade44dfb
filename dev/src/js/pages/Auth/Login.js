import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Helmet } from 'react-helmet';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
    Heading,
    FormLabel,
    InputText,
    Flex,
    Box,
    Paragraph,
    Button,
    FormHelper,
    ToastContext,
    Text,
    Image,
    LoadingBar,
    Separator,
} from '@majoo-ui/react';
import queryString from 'query-string';
import { isEmpty } from 'lodash';
import { EyeOutline, EyeSlashOutline } from '@majoo-ui/icons';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Trans, useTranslation } from 'react-i18next';
import { getAnalytics, logEvent } from '@firebase/analytics';
import { GoogleAuthProvider } from '@firebase/auth';
import { Footer, WelcomeBackground, TurnstileCaptcha } from './component';
import { login as loginService } from '../../services/session';
import logoMajoo from '../../../assets/images/majoo-logo-sm.svg';
import { colors } from '../../stitches.config';
import { catchError } from '../../utils/helper';
import Loading from '../../components/Loading';
import googleIcon from '../../../assets/images/google-icon.webp';
import { signInWithGooglePopup } from '../../config/firebase.util';
import ExpiredModal from './component/ExpiredModal';
import { LIFECYCLE_STATUS } from '../../config/enum';
import { useMediaQuery } from '../../utils/useMediaQuery';

const schema = yup
    .object({
        email: yup.string().email('format').required('email'),
        password: yup.string().required('password'),
    })
    .required();

const Login = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { t, i18n } = useTranslation(['Auth/login']);
    const {
        register,
        handleSubmit,
        watch,
        formState: { errors },
    } = useForm({ resolver: yupResolver(schema) });

    const [isShowPassword, setIsShowPassword] = useState(false);
    const [isDisabled, setIsDisabled] = useState(true);
    const [isLoading, setIsLoading] = useState(false);
    const [showExpiredModal, setShowExpiredModal] = useState(false);
    const [captchaToken, setCaptchaToken] = useState(null);
    const [isTurnstileLoaded, setIsTurnstileLoaded] = useState(false);
    const emailRef = useRef();
    const turnstileRef = useRef(null);

    const { addToast } = React.useContext(ToastContext);
    const isMobile = useMediaQuery('(max-width: 767px)');

    const unknownError = e => {
        addToast({
            id: 'unknown',
            preventDuplicate: true,
            dismissAfter: 3000,
            title: t('toast.error', { ns: 'translation' }),
            variant: 'failed',
            description: e,
        });
    };

    const showUnverifiedToast = () => {
        addToast({
            id: 'unverified',
            preventDuplicate: true,
            dismissAfter: 3000,
            title: t('errors.unverified.title'),
            variant: 'failed',
            description: t('errors.unverified.description'),
        });
    };

    const isButtonDisabled = !isTurnstileLoaded || !captchaToken || isLoading || isDisabled;

    const getButtonText = () => {
        if (!isTurnstileLoaded || (!captchaToken && isTurnstileLoaded)) {
            return t('label.pleaseWait', { ns: 'translation' }, 'Mohon Tunggu Sebentar');
        }
        if (isLoading) {
            return `${t('label.processing', { ns: 'translation' }, 'Memproses...')}...`;
        }
        return t('content.button', 'Masuk');
    };

    const handleTurnstileReady = useCallback((ready) => {
        setIsTurnstileLoaded(ready);
    }, []);

    const onSubmit = async data => {
        setIsLoading(true);

        if (!captchaToken) {
            addToast({
                title: 'Error!',
                variant: 'failed',
                description: 'Recaptcha token has not been success generated',
            });
            setIsLoading(false);
            return;
        }

        const payload = {
            ...(!data.googleToken
                ? {
                      email: data.email,
                      password: data.password,
                  }
                : {
                      token: data.googleToken,
                  }),
            is_cms: 1,
            is_retina: 1,
        };
        let redirectPath;

        const query = queryString.parse(location.search);

        if (query && query.redirect) {
            redirectPath = query.redirect;
            if (query.code) {
                redirectPath += `&code=${query.code}`;
            }
        }

        try {
            await loginService(payload, redirectPath, undefined, navigate, i18n, captchaToken);
        } catch (e) {
            if (e === LIFECYCLE_STATUS.COLD) {
                setShowExpiredModal(true);
                setIsLoading(false);
                return;
            }
            addToast({
                id: 'error',
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
                preventDuplicate: true,
            });
            setIsLoading(false);
        } finally {
            turnstileRef.current?.reset();
        }
    };

    const loginWithGoogle = async () => {
        try {
            const response = await signInWithGooglePopup();
            const credential = GoogleAuthProvider.credentialFromResult(response);
            if (!isEmpty(credential.accessToken)) {
                await onSubmit({
                    googleToken: credential.accessToken,
                });
            }
        } catch (e) {
            if (!e.message.includes('Firebase')) {
                unknownError(e.message);
            } else {
                console.error(e);
            }
        }
    };

    const email = { ...register('email', { required: true }) };

    const handleEmailRef = r => {
        email.ref(r);
        emailRef.current = r;
    };

    useEffect(() => {
        const subscription = watch(value => {
            if (value.email || value.password) {
                setIsDisabled(false);
            } else {
                setIsDisabled(true);
            }
        });
        return () => subscription.unsubscribe();
    }, [watch]);

    useEffect(() => {
        // To check if input autofilled on chrome browser then enable login button
        const interval = setInterval(() => {
            if (emailRef.current) {
                if (emailRef.current.matches('input:-webkit-autofill')) {
                    setIsDisabled(false);
                    clearInterval(interval);
                }
            }
        }, 500);

        return () => {
            clearInterval(interval);
            setIsLoading(false);
        };
    }, []);

    // const switchPage = () => {
    //     const analytics = getAnalytics();
    //     logEvent(analytics, 'switch_existing', {});
    //     window.location.href = process.env.REDIRECT_TO_EXISTING_CMS_URL;
    // };

    return (
        <React.Fragment>
            <Helmet>
                <title>Log In Pengguna | majoo</title>
                <meta
                    name="description"
                    content="Login untuk akses dashboard majoo dan kelola bisnismu dengan lebih mudah"
                />
            </Helmet>
            <Box
                css={{
                    display: 'flex',
                    backgroundColor: '$gray50',
                    flexDirection: 'column',
                    '@lg': {
                        flexDirection: 'row',
                    },
                }}
            >
                <WelcomeBackground />
                <Box
                    css={{
                        display: 'flex',
                        flex: 1,
                        justifyContent: 'flex-start',
                        flexDirection: 'column',
                        alignItems: 'center',
                        paddingTop: 40,
                        '@md': {
                            minHeight: '100vh',
                            justifyContent: 'center',
                            alignItems: 'center',
                            marginBottom: '$spacing-05',
                        },
                        '@lg': {
                            maxWidth: '40vw',
                            mx: '$spacing-04',
                        },
                    }}
                >
                    <Box
                        css={{
                            display: 'flex',
                            alignItems: 'center',
                            flexDirection: 'column',
                            mx: '$spacing-05',
                            width: '100%',
                            maxWidth: 486,
                            paddingLeft: '$compact',
                            paddingRight: '$compact',
                            '@md': { padding: 'unset', marginLeft: '$comfortable', marginRight: '$comfortable' },
                            '@lg': { alignItems: 'flex-start', mt: 'auto', mx: 0 },
                            '& p': { whiteSpace: 'normal', margin: 0 },
                        }}
                    >
                        {/* <Banner */}
                        {/*   css={{ */}
                        {/*     width: '100%', */}
                        {/*     boxShadow: '0px 4px 25px rgba(0, 0, 0, 0.08)', */}
                        {/*     padding: '8px 16px', */}
                        {/*     marginBottom: 24, */}
                        {/*     '@md': { maxWidth: 486, '& > div > div': { flex: 1 } }, */}
                        {/*   }} */}
                        {/* > */}
                        {/*   <Box css={{ display: 'flex', flexDirection: 'column', gap: '4px' }}> */}
                        {/*     <BannerTitle css={{ color: '$textPrimary' }}>{t('banner.title', 'Tampilan Baru Majoo!')}</BannerTitle> */}
                        {/*     <BannerDescription> */}
                        {/*       <Trans t={t} i18nKey="banner.description" defaultValue="Navigasi semakin mudah, bantu bisnismu makin maju!<br/>Untuk pindah ke tampilan lama," /> */}
                        {/*     </BannerDescription> */}
                        {/*     <Flex */}
                        {/*       justify="end" */}
                        {/*       css={{ marginLeft: 4, '& a': { color: '$textGreen' } }} */}
                        {/*     > */}
                        {/*       <Box */}
                        {/*         onClick={switchPage} */}
                        {/*         css={{ cursor: 'pointer', '& a': { pointerEvents: 'none' } }} */}
                        {/*       > */}
                        {/*         <BannerLink> */}
                        {/*           {t('banner.link', 'Klik di Sini')} */}
                        {/*         </BannerLink> */}
                        {/*       </Box> */}
                        {/*     </Flex> */}
                        {/*   </Box> */}
                        {/* </Banner> */}
                        <Image
                            css={{
                                width: 149,
                                height: 48,
                                marginBottom: '28px',
                                display: 'block',
                                '@md': {
                                    width: 186,
                                    height: 60,
                                    margin: 0,
                                    display: 'none',
                                },
                            }}
                            src={logoMajoo}
                            alt="Logo Majoo"
                        />
                        <Box
                            css={{
                                boxShadow: 'none',
                                borderRadius: '$lg',
                                backgroundColor: '$white',
                                padding: '24px 16px 40px',
                                width: '100%',
                                '@md': {
                                    margin: 0,
                                    boxShadow: '$small',
                                    minWidth: 330,
                                    padding: '34px 40px 30px',
                                },
                            }}
                            as="form"
                            method="post"
                            onSubmit={handleSubmit(onSubmit)}
                        >
                            <Flex
                                gap={6}
                                direction="column"
                                align="center"
                                css={{
                                    margin: 'auto',
                                    mb: 32,
                                    '@md': { mb: 24 },
                                }}
                            >
                                <Image
                                    css={{
                                        display: 'none',
                                        width: 149,
                                        height: 48,
                                        '@md': { display: 'block', width: 186, height: 60 },
                                    }}
                                    src={logoMajoo}
                                    alt="Logo Majoo"
                                />
                                <Heading as="h1" heading="h3" align="center">
                                    {t('content.welcome', 'Masuk ke Dashboard')}
                                </Heading>
                                {/* Hide till POS app ready */}
                                {/* <Button
                  type="button"
                  buttonType="ghost-secondary"
                  leftIcon={<img alt="google" src={googleIcon} style={{ width: '24px', height: '24px' }} />}
                  css={{
                    marginTop: '$spacing-03',
                    border: '1px solid $btnDisable',
                    width: '100%',
                    height: '52px',
                  }}
                  onClick={loginWithGoogle}
                  size="lg"
                >
                  {t('content.signInWithGoogle', 'Masuk dengan Google')}
                </Button>
                <Flex align="center" gap={5} css={{ width: '100%' }}>
                  <Separator />
                  <Paragraph paragraph="shortContentRegular">{t('label.or', 'Atau', { ns: 'translation' })}</Paragraph>
                  <Separator />
                </Flex>
                */}
                            </Flex>
                            <TurnstileCaptcha
                                ref={turnstileRef}
                                onTokenChange={setCaptchaToken}
                                onReady={handleTurnstileReady}
                                theme="light"
                                appearance="interaction-only"
                                refreshExpired="auto"
                            />
                            <Flex direction="column">
                                <Box>
                                    <FormLabel htmlFor="email" css={{ marginBottom: 8, color: '$textPrimary' }}>
                                        Email
                                    </FormLabel>
                                    <InputText
                                        id="email"
                                        name="email"
                                        type="text"
                                        placeholder={t('content.placeholder.email', 'Contoh: <EMAIL>')}
                                        onChange={email.onChange}
                                        onBlur={email.onBlur}
                                        ref={handleEmailRef}
                                        isInvalid={!!errors.email}
                                    />
                                </Box>
                                {errors.email && (
                                    <FormHelper error css={{ marginTop: '$spacing-03 !important' }}>
                                        {t(`errors.${errors.email.message}`)}
                                    </FormHelper>
                                )}
                                <Box>
                                    <FormLabel
                                        htmlFor="password"
                                        css={{ color: '$textPrimary', m: '16px 0px 8px', '@md': { m: '24px 0px 8px' } }}
                                    >
                                        {t('content.password', 'Contoh: Sandi123!')}
                                    </FormLabel>
                                    <Box css={{ position: 'relative' }}>
                                        <InputText
                                            id="password"
                                            name="password"
                                            type={isShowPassword ? 'text' : 'password'}
                                            placeholder={t('content.placeholder.password', 'Contoh: Sandi123!')}
                                            {...register('password', { required: true })}
                                            isInvalid={!!errors.password}
                                        />
                                        <Box
                                            onClick={() => {
                                                setIsShowPassword(value => !value);
                                            }}
                                            css={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                cursor: 'pointer',
                                                position: 'absolute',
                                                right: 0,
                                                top: '-20px',
                                                height: 40,
                                                transform: 'translate(-50%, 50%)',
                                            }}
                                        >
                                            {isShowPassword ? <EyeSlashOutline /> : <EyeOutline />}
                                        </Box>
                                    </Box>
                                    {errors.password && (
                                        <FormHelper error css={{ marginTop: '$spacing-03 !important' }}>
                                            {t(`errors.${errors.password.message}`)}
                                        </FormHelper>
                                    )}
                                </Box>
                                <Box css={{ display: 'flex', m: '24px 0px 28px', '@md': { m: '24px 0px 32px' } }}>
                                    <Link to="/auth/forgotPassword">
                                        <Paragraph as="span" paragraph="shortContentBold" color="green">
                                            {t('content.forgot', 'Lupa kata sandi?')}
                                        </Paragraph>
                                    </Link>
                                </Box>
                            </Flex>
                            <Button
                                type="submit"
                                disabled={isButtonDisabled}
                                size="lg"
                                css={{
                                    fontWeight: 600,
                                    fontSize: 16,
                                    width: '100%',
                                    opacity: isButtonDisabled ? 0.6 : 1,
                                    cursor: isButtonDisabled ? 'not-allowed' : 'pointer',
                                    transition: 'all 0.2s ease-in-out',
                                }}
                            >
                                {getButtonText()}
                            </Button>
                            <Box
                                css={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    mt: 32,
                                }}
                            >
                                <Paragraph paragraph="shortContentRegular">
                                    {t('content.accountMessage', 'Belum punya akun majoo? ')}
                                    <Link to="/auth/register">
                                        <Text
                                            as="span"
                                            variant="contentButton"
                                            css={{
                                                ml: 8,
                                                color: '$textGreen',
                                            }}
                                        >
                                            {t('content.register', 'Daftar')}
                                        </Text>
                                    </Link>
                                </Paragraph>
                            </Box>
                        </Box>
                    </Box>
                    <Footer />
                </Box>
            </Box>
            {isLoading && <Loading showLoading={isLoading} label="" />}
            {showExpiredModal && (
                <ExpiredModal open={showExpiredModal} onOpenChange={() => setShowExpiredModal(false)} t={t} />
            )}
        </React.Fragment>
    );
};

export default Login;
