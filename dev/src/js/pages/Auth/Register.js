import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { useForm, Controller } from 'react-hook-form';
import { connect } from 'react-redux';
import {
    Box,
    Image,
    Heading,
    Flex,
    FormLabel,
    FormHelper,
    InputText,
    Paragraph,
    Button,
    Text,
    InputCheckbox,
    ToastContext,
    Tag,
    InputGroup,
    InputRightElement,
} from '@majoo-ui/react';
import { EyeOutline, CheckFilled } from '@majoo-ui/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import queryString from 'query-string';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'lodash';
import { getAnalytics, logEvent } from '@firebase/analytics';
import logoMajoo from '../../../assets/images/majoo-logo-sm.svg';
import { Footer, WelcomeBackground, TurnstileCaptcha } from './component';
import { getErrorCode, catchError, hidePhoneNumber } from '../../utils/helper';
import { postRegister, postRegisterSSO } from '../../actions/userActions';
import { validatePassword } from './register.utils';
import InputPassword from '../../components/InputPassword';
import { colors } from '../../stitches.config';
import OTPModal from './component/OTPModal';
import { requestVerificationCode, validateVerificationCode } from '../../data/users';
import { setConfigStorage } from '../../services/session';
import Loading from '../../components/Loading';

const schema = t =>
    yup
        .object({
            email: yup.string().email('format').required('required'),
            password: yup.string().test('validate-password', 'format', validatePassword).required('required'),
            repeat: yup
                .string()
                .oneOf([yup.ref('password')], 'match')
                .required('required'),
            phone_number: yup
                .string()
                .required(t('form.phoneNumber.errors.mustFilled'))
                .min(8, t('form.phoneNumber.errors.min8Digit'))
                .max(13, t('form.phoneNumber.errors.max13Digit')),
            tos: yup
                .boolean()
                .required('The terms and conditions must be accepted.')
                .oneOf([true], 'The terms and conditions must be accepted.'),
        })
        .required();

const Register = props => {
    const navigate = useNavigate();
    const location = useLocation();
    const query = queryString.parse(location.search);
    const { doRegister, postStatus, postMessage } = props;
    const [isShowRepeat, setIsShowRepeat] = useState(false);
    const [loadingState, setLoadingState] = useState({
        show: false,
        label: 'Loading...',
    });
    // const [ipClient, setIpClient] = useState(false);
    const [utm, setUtm] = useState(null);
    const [otpModalState, setOTPModalState] = useState({
        open: false,
        phoneNumber: '',
        validateCode: '',
        otp: '',
        error: undefined,
    });
    const [phoneValidated, setPhoneValidated] = useState(false);
    const [showInputReferal, setShowInputReferal] = useState(false);
    const [captchaToken, setCaptchaToken] = useState(null);
    const { addToast } = React.useContext(ToastContext);

    const turnstileRef = useRef(null);

    const { t, i18n } = useTranslation(['Auth/register', 'translation']);

    const analytics = getAnalytics();
    const currentPathname = window.location.pathname;

    const {
        register,
        handleSubmit,
        formState: { errors, isValid, isSubmitting },
        getValues,
        control,
        setValue,
        watch,
    } = useForm({
        resolver: yupResolver(schema(t)),
        mode: 'onChange',
        defaultValues: {
            tos: true,
        },
    });

    const watchEmailPhoneNumber = watch(['email', 'phone_number'], ['', '']);

    const unknownError = e => {
        addToast({
            id: 'unknown',
            preventDuplicate: true,
            dismissAfter: 3000,
            title: t('toast.error', { ns: 'translation' }),
            variant: 'failed',
            description: e,
        });
    };

    const onSubmit = async data => {
        setLoadingState({ show: true, label: `${t('form.register', 'Daftar')}...` });

        // if (!isEmpty(data.referal) && await CheckReferal(data.referal) === false) {
        //   setError('referal', { type: 'wrong', message: 'notFound' });
        //   setLoadingState({ show: false });
        //   return;
        // }

        const payload = {
            user_id: data.user_id,
            email: data.email,
            phone_number: data.phone_number,
            password: data.password,
            referral_code: data.referal,
            language: i18n.language,
            user_source: 'trial_web',
            ...(utm && { campaign: utm }),
        };

        try {
            await doRegister(payload);
            // doRegister respond handled by postStatus and postMessage state

            logEvent(analytics, 'Registrasi_Daftar', {
                page_referrer: currentPathname,
                Registrasi_user_email: data.email,
                Registrasi_user_NoWhatsApp: data.phone_number,
            });
        } catch (e) {
            unknownError(e.message);
        } finally {
            setLoadingState({ show: false });
        }
    };

    const requestOTP = async () => {
        setLoadingState({ show: !otpModalState.open, label: t('form.otp.sendButton', 'Kirim OTP') });
        const payload = {
            email: getValues('email', ''),
            phone_number: getValues('phone_number', ''),
            language: i18n.language,
        };
        try {
            if (captchaToken) {
                const res = await requestVerificationCode(payload, captchaToken);
                if (res.data) {
                    setConfigStorage(payload);
                    setValue('user_id', res.data.user_id);

                    logEvent(analytics, 'Registrasi_KirimOTP', {
                        page_referrer: currentPathname,
                        Registrasi_user_email: payload.email,
                        Registrasi_user_NoWhatsApp: payload.phone_number,
                    });

                    if (!otpModalState.open) {
                        setOTPModalState({
                            open: true,
                            phoneNumber: hidePhoneNumber(getValues('phone_number', '')),
                            validateCode: res.data.id_validation_code,
                        });
                    }
                } else {
                    addToast({ title: 'Error!', variant: 'failed', description: res.status.message });
                }
            } else {
                addToast({
                    title: 'Error!',
                    variant: 'failed',
                    description: 'Recaptcha token has not been success generated',
                });
            }
        } catch (e) {
            if (String(getErrorCode(e)) === '4220003' && e.cause && e.cause.data && e.cause.data.user_id) {
                setValue('user_id', e.cause.data.user_id);
                setPhoneValidated(true);
            } else if (e.cause) {
                addToast({ title: 'Error!', variant: 'failed', description: e.cause.status.message });
            }
        } finally {
            turnstileRef.current?.reset();
        }
        setLoadingState({ show: false });
    };

    const validateOTP = async otp => {
        setOTPModalState(current => ({
            ...current,
            open: false,
            otp,
        }));
        setLoadingState({ show: true, label: 'Verification...' });
        try {
            const res = await validateVerificationCode({
                id_validate_code: otpModalState.validateCode,
                code: otp,
            });

            logEvent(analytics, 'Registrasi_VerifikasiNoWhatsapp', {
                page_referrer: currentPathname,
                Registrasi_user_email: getValues('email', ''),
                Registrasi_user_NoWhatsApp: getValues('phone_number', ''),
            });

            if (res.status.code === 2000001) {
                setPhoneValidated(true);
            } else if (res.status) {
                setOTPModalState(current => ({
                    ...current,
                    open: true,
                    error: res.status.message,
                }));
            }
        } catch (e) {
            setOTPModalState(current => ({
                ...current,
                open: true,
                error: t(`errors.code.${getErrorCode(e)}`, catchError(e)),
            }));
        }
        setLoadingState({ show: false });
    };

    useEffect(() => {
        if (!isSubmitting && isEmpty(getValues('google_token', ''))) {
            return;
        }
        if (!postStatus && postMessage !== '') {
            addToast({
                id: 'error',
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(postMessage),
                variant: 'failed',
                preventDuplicate: true,
            });
        }
        if (postStatus) {
            if (isEmpty(getValues('google_token', ''))) {
                setConfigStorage({
                    email: getValues('email'),
                    is_verified: false,
                    status_user: '0',
                });
                navigate(`/auth/verify?email=${getValues('email')}`, { replace: true });
            } else {
                navigate('/auth/phone-verification', { replace: true });
            }
        }
    }, [postStatus, postMessage]);

    useEffect(() => {
        if (Object.keys(query).length > 0) {
            const {
                ref,
                utm_source: utmSource,
                utm_medium: utmMedium,
                utm_campaign: utmCampaign,
                email,
            } = query;
            if (ref) {
                setValue('referal', ref);
            }
            if (utmSource || utmMedium || utmCampaign) {
                setUtm({
                    utm_source: utmSource,
                    utm_medium: utmMedium,
                    utm_campaign: utmCampaign,
                });
            }
            if (email) {
                setValue('email', email);
            }
        }

    }, [JSON.stringify(query)]);

    return (
        <React.Fragment>
            <Box
                css={{
                    display: 'flex',
                    backgroundColor: '$gray50',
                    flexDirection: 'column',
                    '@lg': {
                        flexDirection: 'row',
                    },
                }}
            >
                <WelcomeBackground type="register" />
                <Box
                    css={{
                        display: 'flex',
                        justifyContent: 'flex-start',
                        flexDirection: 'column',
                        alignItems: 'center',
                        flex: 1,
                        mx: '$compact',
                        marginTop: 40,
                        '@md': {
                            justifyContent: 'center',
                            alignItems: 'center',
                            mx: 'auto',
                            my: '$compact',
                        },
                        '@lg': {
                            minWidth: 'unset',
                            maxWidth: '40vw',
                            mx: '$spacing-04',
                        },
                    }}
                >
                    <Image
                        css={{
                            width: 149,
                            height: 48,
                            marginBottom: '28px',
                            display: 'block',
                            '@md': {
                                width: 186,
                                height: 60,
                                margin: 0,
                                display: 'none',
                            },
                        }}
                        src={logoMajoo}
                        alt="Logo Majoo"
                    />
                    <Box
                        css={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '$cozy',
                            boxShadow: 'none',
                            borderRadius: '$lg',
                            backgroundColor: '$white',
                            padding: '24px 16px 40px',
                            width: '100%',
                            maxWidth: 486,
                            '@md': {
                                minWidth: 330,
                                padding: '36px 40px 36px',
                                mt: 'auto',
                                boxShadow: '$small',
                            },
                        }}
                        as="form"
                        method="post"
                        onSubmit={handleSubmit(onSubmit)}
                    >
                        <Image
                            css={{
                                display: 'none',
                                width: 149,
                                height: 48,
                                '@md': {
                                    display: 'block',
                                    width: 186,
                                    height: 60,
                                    alignSelf: 'center',
                                },
                            }}
                            src={logoMajoo}
                            alt="Logo Majoo"
                        />
                        <Heading as="h1" heading="h3" align="center">
                            {t('title', 'Daftar Akun')}
                        </Heading>
                        {/* Hide till POS app ready */}
                        {/* <Button
              type="button"
              buttonType="ghost-secondary"
              leftIcon={<img alt="google" src={googleIcon} style={{ width: '24px', height: '24px' }} />}
              css={{
                border: '1px solid $btnDisable',
                width: '100%',
                height: '52px',
              }}
              onClick={registerWithGoogle}
              size="lg"
            >
              {t('form.google.button', 'Daftar dengan Google')}
            </Button>
            <Flex align="center" gap={5}>
              <Separator />
              <Paragraph paragraph="shortContentRegular">{t('label.or', 'Atau', { ns: 'translation' })}</Paragraph>
              <Separator />
            </Flex>
            */}
                        <Flex direction="column" gap={3}>
                            <Flex direction="column" gap={3}>
                                <FormLabel htmlFor="email" css={{ color: '$textPrimary' }}>
                                    Email
                                </FormLabel>
                                <InputText
                                    id="email"
                                    name="email"
                                    type="text"
                                    placeholder={t('form.email.placeholder', 'Contoh: <EMAIL>')}
                                    {...register('email')}
                                    isInvalid={!!errors.email}
                                    css={{
                                        flex: 1,
                                        backgroundColor: 'unset !important',
                                        color: phoneValidated ? '$textSecondary' : '$textPrimary',
                                        cursor: phoneValidated ? 'not-allowed' : 'text',
                                    }}
                                    readOnly={phoneValidated}
                                />
                                {errors.email && (
                                    <FormHelper error>{t(`form.email.errors.${errors.email.message}`)}</FormHelper>
                                )}
                            </Flex>
                            <Flex direction="column" gap={3}>
                                <TurnstileCaptcha
                                    ref={turnstileRef}
                                    sitekey={process.env.TURNSTILE_SITE_KEY}
                                    onTokenChange={setCaptchaToken}
                                    theme="light"
                                    appearance="interaction-only"
                                    refreshExpired="auto"
                                />
                                <FormLabel htmlFor="phone_number" css={{ color: '$textPrimary' }}>
                                    {t('form.phoneNumber.label', 'Nomor WhatsApp')}
                                </FormLabel>
                                <Flex gap={6}>
                                    <InputGroup css={{ flex: 1 }}>
                                        <InputText
                                            id="phone_number"
                                            name="phone_number"
                                            type="number"
                                            placeholder={`${t('placeholder.example', 'Contoh: ', {
                                                ns: 'translation',
                                            })}0812 xxxx xxxx`}
                                            isInvalid={!!errors.phone_number}
                                            {...register('phone_number')}
                                            css={{
                                                flex: 1,
                                                color: phoneValidated ? '$textSecondary' : '$textPrimary',
                                                cursor: phoneValidated ? 'not-allowed' : 'text',
                                            }}
                                            readOnly={phoneValidated}
                                        />
                                        <InputRightElement>
                                            {phoneValidated && <CheckFilled color={colors.iconGreen} />}
                                        </InputRightElement>
                                    </InputGroup>
                                    {!phoneValidated && (
                                        <Button
                                            type="button"
                                            disabled={
                                                !captchaToken ||
                                                !watchEmailPhoneNumber[0] ||
                                                !watchEmailPhoneNumber[1] ||
                                                errors.phone_number ||
                                                errors.email
                                            }
                                            onClick={requestOTP}
                                        >
                                            {t('form.otp.sendButton', 'Kirim OTP')}
                                        </Button>
                                    )}
                                </Flex>
                                {errors.phone_number && <FormHelper error>{errors.phone_number.message}</FormHelper>}
                            </Flex>
                        </Flex>

                        {phoneValidated && (
                            <React.Fragment>
                                <Controller
                                    name="password"
                                    control={control}
                                    render={({ field }) => (
                                        <InputPassword
                                            name="password"
                                            isInvalid={!!errors.password}
                                            value={field.value}
                                            onChange={field.onChange}
                                            onBlur={field.onBlur}
                                            maxLength={20}
                                            label={t('form.password.label', 'Kata Sandi')}
                                        />
                                    )}
                                />
                                <Flex direction="column" gap={3}>
                                    <FormLabel htmlFor="repeat" css={{ color: '$textPrimary' }}>
                                        {t('form.confirmPassword.label', 'Konfirmasi Kata Sandi')}
                                    </FormLabel>
                                    <Box css={{ position: 'relative' }}>
                                        <InputText
                                            id="repeat"
                                            name="repeat"
                                            type={isShowRepeat ? 'text' : 'password'}
                                            placeholder={t('form.confirmPassword.placeholder', 'Contoh: Sandi123!')}
                                            {...register('repeat', { required: true })}
                                            isInvalid={!!errors.repeat}
                                            maxLength={20}
                                        />
                                        <Box
                                            onClick={() => {
                                                setIsShowRepeat(value => !value);
                                            }}
                                            css={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                cursor: 'pointer',
                                                position: 'absolute',
                                                right: 0,
                                                top: '-20px',
                                                height: 40,
                                                transform: 'translate(-50%, 50%)',
                                            }}
                                        >
                                            <EyeOutline />
                                        </Box>
                                    </Box>
                                    {errors.repeat && (
                                        <FormHelper error>
                                            {t(`form.confirmPassword.errors.${errors.repeat.message}`)}
                                        </FormHelper>
                                    )}
                                </Flex>
                            </React.Fragment>
                        )}
                        {showInputReferal || (getValues('referal', '') && getValues('referal', '').length > 0) ? (
                            <Flex direction="column" gap={3}>
                                <FormLabel
                                    htmlFor="referal"
                                    id="Opsional"
                                    css={{
                                        color: '$textPrimary',
                                        '& span:nth-child(2)': { color: '$textSecondary' },
                                    }}
                                >
                                    {t('form.referal.label', 'Kode Referal')}
                                    <Tag
                                        css={{
                                            borderRadius: '100px',
                                            backgroundColor: '$gray150',
                                            ml: '$spacing-03',
                                            padding: '4px 8px',
                                            color: '$textSecondary',
                                        }}
                                    >
                                        {t('form.referal.subLabel', 'Opsional')}
                                    </Tag>
                                </FormLabel>
                                <InputText
                                    id="referal"
                                    name="referal"
                                    type="text"
                                    placeholder={t('form.referal.placeholder', 'Masukkan kode referal kamu')}
                                    {...register('referal')}
                                    isInvalid={!!errors.referal}
                                />
                                {errors.referal && (
                                    <FormHelper error>{t(`form.referal.errors.${errors.referal.message}`)}</FormHelper>
                                )}
                            </Flex>
                        ) : (
                            <Flex gap={3} align="center">
                                <Paragraph color="primary">
                                    {t('form.referal.haveReferralLabel', 'Punya kode referral?')}
                                </Paragraph>
                                <Paragraph
                                    paragraph="shortContentBold"
                                    color="green"
                                    onClick={() => setShowInputReferal(true)}
                                    css={{ cursor: 'pointer' }}
                                >
                                    {t('label.use', 'Gunakan', { ns: 'translation' })}
                                </Paragraph>
                            </Flex>
                        )}
                        <Flex align="start">
                            <Flex align="center" justify="center" css={{ m: '4px 12px 4px 4px' }}>
                                <Controller
                                    name="tos"
                                    control={control}
                                    render={({ field: { onChange, value } }) => (
                                        <InputCheckbox
                                            id="tos"
                                            value="tos"
                                            checked={value}
                                            onCheckedChange={onChange}
                                        />
                                    )}
                                />
                            </Flex>
                            <Box css={{ maxWidth: 325 }}>
                                <Text variant="helper">
                                    {t(
                                        'form.toc.label',
                                        'Dengan mendaftar, saya menyatakan telah membaca dan menyetujui ',
                                    )}
                                    <Box
                                        as="a"
                                        href={`${process.env.PORTAL_BASE_URL}/syarat-dan-ketentuan`}
                                        css={{ color: '$textGreen' }}
                                    >
                                        {t('form.toc.terms', 'Ketentuan Layanan')}
                                    </Box>
                                    &nbsp;&&nbsp;
                                    <Box
                                        as="a"
                                        href={`${process.env.PORTAL_BASE_URL}/privasi-dan-kebijakan`}
                                        css={{ color: '$textGreen' }}
                                    >
                                        {t('form.toc.condition', 'Kebijakan Majoo')}
                                    </Box>
                                </Text>
                            </Box>
                        </Flex>
                        <Button
                            type="submit"
                            size="lg"
                            css={{
                                mt: 24,
                                fontWeight: 600,
                                fontSize: 16,
                                width: '100%',
                            }}
                            disabled={!isValid || loadingState.show}
                        >
                            {t('form.register', 'Daftar')}
                        </Button>
                        <Box
                            css={{
                                display: 'flex',
                                justifyContent: 'center',
                                marginTop: 24,
                            }}
                        >
                            <Paragraph paragraph="shortContentRegular">
                                {t('form.registered', 'Sudah punya akun majoo? ')}
                                <Link to="/auth/login">
                                    <Text
                                        as="span"
                                        variant="contentButton"
                                        css={{
                                            color: '$textGreen',
                                            marginLeft: '$spacing-05',
                                        }}
                                    >
                                        {t('form.signIn', 'Masuk')}
                                    </Text>
                                </Link>
                            </Paragraph>
                        </Box>
                    </Box>
                    <Footer />
                </Box>
            </Box>
            {otpModalState.open && (
                <OTPModal
                    modalState={otpModalState}
                    onOpenChange={open =>
                        setOTPModalState(current => ({
                            ...current,
                            open,
                        }))
                    }
                    requestOTP={requestOTP}
                    validateOTP={validateOTP}
                    t={t}
                />
            )}
            {loadingState.show && <Loading showLoading={loadingState.show} label={loadingState.label} />}
        </React.Fragment>
    );
};

Register.propTypes = {
    doRegister: PropTypes.func.isRequired,
    doRegisterSSO: PropTypes.func.isRequired,
    postMessage: PropTypes.string,
    postStatus: PropTypes.bool,
};

Register.defaultProps = {
    postMessage: '',
    postStatus: false,
};

export default 
    connect(
        state => ({
            postStatus: state.user.status,
            postMessage: state.user.message,
        }),
        dispatch => ({
            doRegister: payload => dispatch(postRegister(payload)),
            doRegisterSSO: payload => dispatch(postRegisterSSO(payload)),
        }),
    )(Register);
