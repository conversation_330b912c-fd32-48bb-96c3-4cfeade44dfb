import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { connect } from 'react-redux';
import {
    Box,
    Image,
    Heading,
    Flex,
    FormLabel,
    FormHelper,
    InputText,
    Paragraph,
    Button,
    Text,
    LoadingBar,
    ToastContext,
} from '@majoo-ui/react';
import { CheckFilled } from '@majoo-ui/icons';

import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Trans, useTranslation } from 'react-i18next';
import { resetPassword } from '../../actions/userActions';
import logoMajoo from '../../../assets/images/majoo-logo-sm.svg';

import { Footer, WelcomeBackground, TurnstileCaptcha } from './component';
import { colors } from '../../stitches.config';
import { useMediaQuery } from '../../utils/useMediaQuery';
import { catchError } from '../../utils/helper';

const schema = yup
    .object({
        email: yup.string().email('format').required('required'),
    })
    .required();

const ForgotPassword = props => {
    const { resetPassword: _resetPassword } = props;
    const {
        register,
        handleSubmit,
        formState: { errors },
        watch,
        getValues,
    } = useForm({ resolver: yupResolver(schema) });
    const [isDisabled, setIsDisabled] = useState(true);
    const [isResetted, setIsResetted] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [captchaToken, setCaptchaToken] = useState(null);
    const isMobile = useMediaQuery('(max-width: 767px)');

    const { t } = useTranslation(['Auth/forgot', 'translation']);

    const { addToast } = React.useContext(ToastContext);

    const turnstileRef = useRef(null);

    const onSubmit = async data => {
        setIsLoading(true);
        try {
            if (captchaToken) {
                const result = await _resetPassword(data.email, captchaToken);
                if (result.status) {
                    setIsResetted(true);
                } else if (result.msg) {
                    addToast({
                        id: 'error',
                        title: t('toast.error', { ns: 'translation' }),
                        description: result.msg,
                        variant: 'failed',
                        preventDuplicate: true,
                    });
                }
            } else {
                addToast({
                    id: 'error',
                    title: t('toast.error', { ns: 'translation' }),
                    description: 'Recaptcha token has not been success generated',
                    variant: 'failed',
                    preventDuplicate: true,
                });
            }
        } catch (error) {
            addToast({
                id: 'error',
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
                preventDuplicate: true,
            });
        } finally {
            turnstileRef.current?.reset();
            setIsLoading(false);
        }
    };

    useEffect(() => {
        const subscription = watch(value => {
            if (value.email || value.password) {
                setIsDisabled(false);
            } else {
                setIsDisabled(true);
            }
        });
        return () => subscription.unsubscribe();
    }, [watch]);

    return (
        <React.Fragment>
            <LoadingBar
                showProgressOnly
                show={isLoading}
                colors={[colors.primary200, colors.primary500]}
                size="sm"
                css={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100% !important',
                    padding: '0 !important',
                }}
            />
            <Box
                css={{
                    display: 'flex',
                    backgroundColor: '$gray50',
                    flexDirection: 'column',
                    '@lg': {
                        flexDirection: 'row',
                    },
                }}
            >
                <WelcomeBackground />
                <Box
                    css={{
                        display: 'flex',
                        justifyContent: 'flex-start',
                        flexDirection: 'column',
                        alignItems: 'center',
                        flex: 1,
                        mx: '$compact',
                        marginTop: 40,
                        '@md': {
                            minWidth: 598,
                            justifyContent: 'center',
                            alignItems: 'center',
                            mx: 0,
                            my: '$compact',
                        },
                    }}
                >
                    <Box
                        css={{
                            boxShadow: '$small',
                            borderRadius: '$lg',
                            backgroundColor: '$white',
                            padding: '24px 16px 40px',
                            width: '100%',
                            '@md': { width: 486, padding: '24px 40px 32px', mt: 'auto' },
                        }}
                        as="form"
                        method="post"
                        onSubmit={handleSubmit(onSubmit)}
                    >
                        {isResetted ? (
                            <React.Fragment>
                                <Flex
                                    gap={5}
                                    direction="column"
                                    align="center"
                                    css={{
                                        margin: 'auto',
                                        mb: 32,
                                        '@md': { mb: 24 },
                                    }}
                                >
                                    <Image src={logoMajoo} alt="Logo Majoo" width={186} height={60} />
                                    <Box
                                        css={{
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            mt: '$compact',
                                            width: 'auto',
                                            height: 40,
                                            '@md': { mb: '$compact' },
                                        }}
                                    >
                                        <CheckFilled size={isMobile ? 55 : 86} color={colors.textGreen} />
                                    </Box>
                                    <Heading as="h1" heading="h3" align="center">
                                        {t('title2', 'Email Terkirim')}
                                    </Heading>
                                </Flex>
                                <Box css={{ margin: 'auto', mb: 24, '@md': { maxWidth: 346 } }}>
                                    <Paragraph paragraph="longContentRegular" align="center">
                                        <Trans
                                            t={t}
                                            i18nKey="subTitle2"
                                            defaultValue={`Tautan untuk mengatur ulang kata sandi Anda telah dikirimkan ke ${getValues(
                                                'email',
                                            )}. ikuti petunjuk untuk melakukan reset kata sandi.`}
                                        >
                                            {{ email: getValues('email') }}
                                        </Trans>
                                    </Paragraph>
                                </Box>
                                <Paragraph paragraph="shortContentRegular" align="center">
                                    <Link to="/auth/login">
                                        <Text
                                            as="span"
                                            variant="contentButton"
                                            css={{
                                                margin: 0,
                                                color: '$textGreen',
                                                '@md': { marginLeft: 8 },
                                            }}
                                        >
                                            {t('back', 'Kembali ke halaman masuk')}
                                        </Text>
                                    </Link>
                                </Paragraph>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                <Flex
                                    gap={5}
                                    direction="column"
                                    align="center"
                                    css={{
                                        margin: 'auto',
                                        mb: 32,
                                        '@md': { mb: 24 },
                                    }}
                                >
                                    <Image src={logoMajoo} alt="Logo Majoo" width={186} height={60} />
                                    <Heading as="h1" heading="h3" align="center">
                                        {t('title', 'Atur Ulang kata Sandi')}
                                    </Heading>
                                </Flex>
                                <Box
                                    css={{
                                        margin: 'auto',
                                        mb: 24,
                                        '@md': { maxWidth: 311, mb: 32 },
                                    }}
                                >
                                    <Paragraph paragraph="longContentRegular" align="center">
                                        {t(
                                            'subTitle',
                                            'Masukkan Email yang terhubung dengan akun majoo untuk mengatur ulang kata sandi',
                                        )}
                                    </Paragraph>
                                </Box>
                                <Box>
                                    <FormLabel htmlFor="email" css={{ marginBottom: 8, color: '$textPrimary' }}>
                                        Email
                                    </FormLabel>
                                    <InputText
                                        id="email"
                                        name="email"
                                        type="text"
                                        placeholder={t('email.placeholder', 'Contoh: <EMAIL>')}
                                        isInvalid={!!errors.email}
                                        {...register('email')}
                                    />
                                </Box>
                                {errors.email && (
                                    <FormHelper error css={{ marginTop: '$spacing-03 !important' }}>
                                        {t(`email.errors.${errors.email.message}`)}
                                    </FormHelper>
                                )}
                                <TurnstileCaptcha
                                    ref={turnstileRef}
                                    sitekey={process.env.TURNSTILE_SITE_KEY}
                                    onTokenChange={setCaptchaToken}
                                    theme="light"
                                    appearance="interaction-only"
                                    refreshExpired="auto"
                                    style={{
                                        marginTop: 8,
                                    }}
                                />
                                <Button
                                    type="submit"
                                    size="lg"
                                    css={{
                                        mt: 24,
                                        fontWeight: 600,
                                        fontSize: 16,
                                        width: '100%',
                                    }}
                                    disabled={!captchaToken || isDisabled || isLoading}
                                >
                                    {t('button', 'Atur Ulang Kata Sandi')}
                                </Button>
                                <Box
                                    css={{
                                        display: 'flex',
                                        justifyContent: 'center',
                                        marginTop: 24,
                                        '@md': { marginTop: 16 },
                                    }}
                                >
                                    <Paragraph paragraph="shortContentRegular">
                                        {t('cancel', 'Batal? ')}
                                        <Link to="/auth/login">
                                            <Text
                                                as="span"
                                                variant="contentButton"
                                                css={{
                                                    margin: 0,
                                                    color: '$textGreen',
                                                    '@md': { marginLeft: 16 },
                                                }}
                                            >
                                                {t('login', 'Masuk di sini')}
                                            </Text>
                                        </Link>
                                    </Paragraph>
                                </Box>
                            </React.Fragment>
                        )}
                    </Box>
                    <Footer />
                </Box>
            </Box>
        </React.Fragment>
    );
};

ForgotPassword.propTypes = {
    resetPassword: PropTypes.func,
};

ForgotPassword.defaultProps = {
    resetPassword: () => {},
};

export default connect(null, dispatch => ({
    resetPassword: (email, captchaToken) => dispatch(resetPassword(email, captchaToken)),
}))(ForgotPassword);
