import React, { forwardRef, useCallback, useContext, useEffect, useImperativeHandle, useRef } from 'react';
import Turnstile, { useTurnstile } from 'react-turnstile';
import { ToastContext } from '@majoo-ui/react';
import { useTranslation } from 'react-i18next';
import useBrowserInfo from '../../../utils/useBrowserInfo';

const FALLBACK_MESSAGES = {
    default: 'Verifikasi captcha gagal. Silakan coba lagi dalam beberapa saat.',
    initializationFailure: 'Turnstile gagal diinisialisasi. Muat ulang halaman dan coba lagi.',
    invalidParameters:
        'Parameter verifikasi tidak valid. Muat ulang halaman atau coba lagi setelah beberapa saat.',
    apiCompatibility:
        'Versi verifikasi yang digunakan tidak kompatibel. Muat ulang halaman dan coba kembali.',
    invalidSiteKey:
        'Kunci verifikasi tidak valid. <PERSON>bungi tim majoo jika masalah ini terus berlanjut.',
    domainNotAllowed: 'Verifikasi tidak diizinkan pada domain ini.  Hubungi tim majoo jika masalah ini terus berlanjut.',
    invalidAction:
        'Parameter aksi verifikasi tidak dikenali. Muat ulang halaman dan coba lagi setelah beberapa saat.',
    invalidCustomData:
        'Data tambahan verifikasi tidak valid. Muat ulang halaman dan coba lagi setelah beberapa saat.',
    unsupportedBrowser:
        'Browser Anda belum didukung untuk verifikasi. Gunakan browser modern yang terbaru.',
    inconsistentUserAgent:
        'Kami mendeteksi perubahan user-agent. Nonaktifkan ekstensi yang memodifikasi browser (VPN, AdBlock, Captcha Solver, dll) dan coba lagi.',
    timeout: 'Sesi verifikasi berakhir karena kehabisan waktu. Muat ulang captcha dan coba lagi.',
    staleInteractive:
        'Sesi verifikasi interaktif telah kedaluwarsa. Muat ulang captcha dan coba lagi dengan segera.',
    internalError:
        'Terjadi gangguan internal pada layanan verifikasi. Coba lagi beberapa saat lagi.',
    cachedWidget:
        'Komponen verifikasi tersimpan di cache. Bersihkan cache browser kemudian coba lagi.',
    incorrectClientTime:
        'Waktu atau tanggal di perangkat Anda tampaknya tidak akurat. Harap perbarui pengaturan Anda untuk memastikan semua fitur aplikasi berjalan dengan lancar.',
    iframeBlocked:
        'Kami tidak dapat memuat verifikasi karena pengaturan keamanan browser. Izinkan konten dari challenges.cloudflare.com atau coba browser lain.',
    genericExecution:
        'Terjadi kesalahan saat menjalankan verifikasi. Nonaktifkan sementara ekstensi yang menghalangi (VPN, AdBlock, Captcha Solver, dll) dan coba lagi.',
    invalidSize: 'Pengaturan ukuran verifikasi tidak valid. Muat ulang halaman dan coba kembali.',
    invalidTheme: 'Pengaturan tema verifikasi tidak valid. Muat ulang halaman dan coba kembali.',
    challengeFailure:
        'Verifikasi tidak dapat diselesaikan. Coba lagi dan pastikan tidak ada ekstensi yang memblokir captcha (VPN, AdBlock, Captcha Solver, dll).',
};

const DEFAULT_TURNSTILE_ERROR_MESSAGE = FALLBACK_MESSAGES.default;

const TURNSTILE_ERROR_MESSAGES = [
    { prefixes: ['100'], key: 'initializationFailure' },
    { prefixes: ['102', '103', '104', '106'], key: 'invalidParameters' },
    { prefixes: ['105'], key: 'apiCompatibility' },
    { codes: ['110100', '110110', '400020'], key: 'invalidSiteKey' },
    { codes: ['110200'], key: 'domainNotAllowed' },
    { codes: ['110420'], key: 'invalidAction' },
    { codes: ['110430'], key: 'invalidCustomData' },
    { prefixes: ['110500'], key: 'unsupportedBrowser' },
    { prefixes: ['110510'], key: 'inconsistentUserAgent' },
    { prefixes: ['11060'], key: 'timeout' },
    { prefixes: ['11062'], key: 'staleInteractive' },
    { prefixes: ['120'], key: 'internalError' },
    { codes: ['200010'], key: 'cachedWidget' },
    { codes: ['200100'], key: 'incorrectClientTime' },
    { codes: ['200500'], key: 'iframeBlocked' },
    { prefixes: ['300'], key: 'genericExecution' },
    { codes: ['400030'], key: 'invalidSize' },
    { codes: ['400040'], key: 'invalidTheme' },
    { prefixes: ['600'], key: 'challengeFailure' },
];

const extractFromString = value => {
    if (!value) return undefined;
    const stringValue = value.toString();
    const numericMatch = stringValue.match(/\d{3,6}/);
    return numericMatch ? numericMatch[0] : stringValue;
};

const getTurnstileErrorDescription = (errorValue, translate) => {
    if (!errorValue) {
        return {
            code: undefined,
            description: translate('default', DEFAULT_TURNSTILE_ERROR_MESSAGE),
        };
    }

    let code;

    if (typeof errorValue === 'string' || typeof errorValue === 'number') {
        code = extractFromString(errorValue);
    } else if (typeof errorValue === 'object') {
        code =
            extractFromString(errorValue.code) ||
            extractFromString(errorValue.error) ||
            extractFromString(errorValue.message);
    }

    if (!code) {
        return {
            code: undefined,
            description: translate('default', DEFAULT_TURNSTILE_ERROR_MESSAGE),
        };
    }

    const matchedMessage = TURNSTILE_ERROR_MESSAGES.find(item => {
        if (item.codes && item.codes.includes(code)) {
            return true;
        }
        if (item.prefixes) {
            return item.prefixes.some(prefix => code.startsWith(prefix));
        }
        return false;
    });

    const messageKey = matchedMessage ? matchedMessage.key : 'default';
    const fallback = FALLBACK_MESSAGES[messageKey] || DEFAULT_TURNSTILE_ERROR_MESSAGE;

    return {
        code,
        description: translate(messageKey, fallback),
    };
};

const TurnstileCaptcha = React.memo(forwardRef((props, ref) => {
    const {
        siteKey = process.env.TURNSTILE_SITE_KEY,
        appearance = 'interaction-only',
        theme = 'light',
        refreshExpired = 'auto',
        onTokenChange,
        onReady,
        onVerify,
        onError,
        onTimeout,
        onUnsupported,
        toastIdPrefix = 'turnstile-error',
        ...rest
    } = props;

    const turnstile = useTurnstile();
    const { addToast } = useContext(ToastContext);
    const { t } = useTranslation(['Auth/captcha', 'translation']);
    const browserInfo = useBrowserInfo();
    const retryState = useRef({ attempts: 0, timer: null });

    const translateMessage = useCallback(
        (key, fallback) => t(`messages.${key}`, { ns: 'Auth/captcha', defaultValue: fallback }),
        [t],
    );

    useEffect(() => {
        if (!onReady) {
            return undefined;
        }

        let cancelled = false;

        const notifyReady = value => {
            if (!cancelled) {
                onReady(value);
            }
        };

        const checkTurnstileReady = () => {
            if (typeof window !== 'undefined' && window.turnstile) {
                notifyReady(true);
            } else if (!cancelled) {
                setTimeout(checkTurnstileReady, 100);
            }
        };

        checkTurnstileReady();

        return () => {
            cancelled = true;
            onReady(false);
        };
    }, [onReady]);

    const safeReset = useCallback(() => {
        const state = retryState.current;
        const { attempts, timer } = state;

        if (timer) {
            clearTimeout(timer);
            state.timer = null;
        }

        if (attempts >= 5) {
            return;
        }

        state.attempts = attempts + 1;
        state.timer = setTimeout(() => {
            try {
                if (turnstile && typeof turnstile.reset === 'function') {
                    turnstile.reset();
                }
            } catch (err) {
                console.warn('Failed to reset Turnstile widget', err);
            } finally {
                state.timer = null;
            }
        }, 1000);
    }, [turnstile]);

    useEffect(() => {
        const currentRetryState = retryState.current;
        return () => {
            if (currentRetryState?.timer) {
                clearTimeout(currentRetryState.timer);
                currentRetryState.timer = null;
            }
        };
    }, []);

    useImperativeHandle(
        ref,
        () => ({
            reset: () => {
                if (onTokenChange) {
                    onTokenChange(null);
                }
                retryState.current.attempts = 0;
                safeReset();
            },
            execute: (...args) => {
                if (typeof turnstile.execute === 'function') {
                    return turnstile.execute(...args);
                }
                return undefined;
            },
        }),
        [onTokenChange, safeReset, turnstile],
    );

    const showErrorToast = (code, description) => {
        addToast({
            id: `${toastIdPrefix}-${code || 'unknown'}`,
            preventDuplicate: true,
            title: t('toast.error', { ns: 'translation' }, 'Terjadi Kesalahan'),
            variant: 'failed',
            description,
        });
    };

    const handleVerify = token => {
        const state = retryState.current;
        if (state.timer) {
            clearTimeout(state.timer);
            state.timer = null;
        }
        state.attempts = 0;

        if (onTokenChange) {
            onTokenChange(token);
        }
        if (onVerify) {
            onVerify(token);
        }
    };

    const handleError = (error, bound) => {
        const { code, description } = getTurnstileErrorDescription(error, translateMessage);

        showErrorToast(code, description);
        console.error({ error, bound, browserInfo });

        if (onError) {
            onError(error, bound);
        } else {
            safeReset();

            if (onTokenChange) {
                onTokenChange(null);
            }
        }
    };

    const handleTimeout = boundTurnstile => {
        const { code, description } = getTurnstileErrorDescription('11060', translateMessage);
        
        showErrorToast(code, description);

        if (onTimeout) {
            onTimeout(boundTurnstile);
        } else {
            safeReset();

            if (onTokenChange) {
                onTokenChange(null);
            }
        }
    };

    const handleUnsupported = () => {
        const { code, description } = getTurnstileErrorDescription('110500', translateMessage);

        showErrorToast(code, description);

        if (onUnsupported) {
            onUnsupported();
        }
    };

    return (
        <Turnstile
            sitekey={siteKey}
            appearance={appearance}
            theme={theme}
            refreshExpired={refreshExpired}
            onVerify={handleVerify}
            onError={handleError}
            onTimeout={handleTimeout}
            onUnsupported={handleUnsupported}
            {...rest}
        />
    );
}));

TurnstileCaptcha.displayName = 'TurnstileCaptcha';

export default TurnstileCaptcha;
