import React, { useEffect, useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
    Box,
    Paper,
    Separator,
    Table,
    ToastContext,
    Paragraph,
    TagStatus,
    Tooltip,
    IconButton,
    Image,
    Flex,
    Text,
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
} from '@majoo-ui/react';
import { EyeOutline, EditOutline, DownloadOutline, EllipsisHorizontalOutline } from '@majoo-ui/icons';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { debounce } from 'lodash';

import { useTranslation } from 'react-i18next';
import * as outletActions from '../../../../data/outlets/actions';
import CoreHOC from '../../../../core/CoreHOC';
import { isMobile } from '../../../../config/config';
import { catchError, isNullOrUndefined } from '../../../../utils/helper';
import { getBank } from '../../../../data/outlets';
import {
    getListSubmissionProfile,
    getDetailSubmissionProfile,
    createSubmissionProfile,
    updateSubmissionProfile,
    deleteSubmissionProfile,
    changeStatusActiveRekening,
    userRekeningPayrollStatus,
    patchSubmissionProfile,
    setSettlement,
} from '../../../../data/users';
import { AccountOptionModal, ConfirmAddNewAcc } from './retina/Modal';

import { ACCOUNT_TYPE } from './retina/enum';
import { STATUS_TYPE, BANK_ID } from './constants';
import { TitleSection, FilterSection, AddBankAccountForm } from './retina';

import emptyData from './components/doodle-empty-account.png';
import { ROLES } from '../../Employee/Employee/settings/constants';
import { BannerText } from '../../../../components/retina';
import { analyticLogEvent } from '../../../../v2-utils';
import SettlementAccountModal from './retina/SettlementAccountModal';

const handleAnalyticLogEventBankRaya = ({ userEmail, userStrap }) => {
    const payload = {
        user_email: userEmail,
        outlet_id: userStrap.branchId,
    };
    analyticLogEvent('add_bank_account_choose_bank_raya', payload);
};

const handleAnalyticLogEventAddBankAccount = ({ userEmail, userStrap }) => {
    const payload = {
        user_email: userEmail,
        outlet_id: userStrap.branchId,
    };
    analyticLogEvent('add_bank_account', payload);
};

const headerData = (onRowAction, t) => [
    {
        Header: t('header.name', 'Nama Bank'),
        accessor: 'bank_name',
        Cell: instance => {
            const { value } = instance;
            return (
                <Box css={{ maxWidth: 200 }}>
                    <Paragraph isTruncated>{value}</Paragraph>
                </Box>
            );
        },
    },
    {
        Header: t('header.no', 'No Rekening'),
        accessor: 'account_no',
        Cell: instance => {
            const { value } = instance;
            return (
                <Box css={{ maxWidth: 200 }}>
                    <Paragraph isTruncated>{value}</Paragraph>
                </Box>
            );
        },
    },
    {
        Header: t('header.owner', 'Pemegang Rekening'),
        accessor: 'account_holder',
        Cell: instance => {
            const { value } = instance;
            return (
                <Box css={{ maxWidth: 200 }}>
                    <Paragraph isTruncated>{value}</Paragraph>
                </Box>
            );
        },
    },
    {
        Header: 'Outlet',
        accessor: 'outlet_name',
        Cell: instance => {
            const { value } = instance;
            return (
                <Box css={{ maxWidth: 200 }}>
                    <Tooltip label={value} align="start">
                        <Paragraph isTruncated>{value}</Paragraph>
                    </Tooltip>
                </Box>
            );
        },
    },
    {
        Header: 'Settlement',
        accessor: 'ewallet_settlement',
        unsortable: true,
        Cell: instance => {
            const {
                row: { original },
            } = instance;
            const ewalletSettlement = original.ewallet_settlement || [];
            const tokoOnlineSettlement = original.toko_online_settlement || [];

            const getOutletNames = settlements =>
                settlements.map(item => item?.outlet_name || item?.name).filter(Boolean);

            const formatList = settlements => {
                const names = getOutletNames(settlements);
                if (!names.length) {
                    return null;
                }
                const visibleNames = names.slice(0, 2);
                const remaining = names.length - visibleNames.length;
                return {
                    label: visibleNames.join(', '),
                    remaining,
                };
            };

            const settlementRows = [
                { label: 'QRIS:', value: formatList(ewalletSettlement) },
                { label: 'Toko Online:', value: formatList(tokoOnlineSettlement) },
            ].filter(item => item.value);

            if (!settlementRows.length) {
                return (
                    <Box css={{ maxWidth: 200 }}>
                        <Paragraph>-</Paragraph>
                    </Box>
                );
            }

            return (
                <Box css={{ maxWidth: 200, display: 'flex', flexDirection: 'column', rowGap: '8px' }}>
                    {settlementRows.map(item => (
                        <Box key={item.label}>
                            <Paragraph paragraph="shortContentBold">{item.label}</Paragraph>
                            <Paragraph css={{ whiteSpace: 'normal' }}>
                                {item.value.label}
                                {item.value.remaining > 0 && (
                                    <Text as="span" css={{ fontWeight: 600 }}>{` +${item.value.remaining}`}</Text>
                                )}
                            </Paragraph>
                        </Box>
                    ))}
                </Box>
            );
        },
    },
    {
        Header: 'Perubahan Settlement',
        accessor: 'status_request_settlement_id',
        Cell: instance => {
            const { value } = instance;
            if (!value) return '-';
            switch (String(value)) {
                case STATUS_TYPE.REJECTED:
                    return <TagStatus type="error">Ditolak</TagStatus>;
                default:
                    return <TagStatus type="process">Diproses</TagStatus>;
            }
        },
    },
    {
        Header: 'Status',
        accessor: 'status_id',
        Cell: instance => {
            const { value } = instance;
            switch (String(value)) {
                case STATUS_TYPE.REJECTED:
                    return <TagStatus type="error">{t('status.rejected', 'Ditolak')}</TagStatus>;
                case STATUS_TYPE.APPROVED:
                    return <TagStatus type="success">{t('status.verified', 'Terverifikasi')}</TagStatus>;
                case STATUS_TYPE.INACTIVE:
                    return <TagStatus type="success">{t('status.verified', 'Terverifikasi')}</TagStatus>;
                case STATUS_TYPE.INTEGRATED:
                    return <TagStatus type="success">{t('status.connected', 'Terhubung')}</TagStatus>;
                default:
                    return (
                        <TagStatus type="new" css={{ maxWidth: 'unset' }}>
                            {t('status.pending', 'Menunggu Verifikasi')}
                        </TagStatus>
                    );
            }
        },
    },
    {
        accessor: 'id',
        Header: '',
        unsortable: true,
        Cell: instance => {
            const {
                row: { original },
            } = instance;
            const { status_id, status_request_settlement_id } = original;
            if (!!status_request_settlement_id) {
                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <IconButton>
                                <EllipsisHorizontalOutline size={20} color="currentColor" />
                            </IconButton>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent sideOffset={5} css={{ width: 208 }}>
                            {String(status_id) === STATUS_TYPE.REJECTED ? (
                                <DropdownMenuItem onClick={() => onRowAction({ type: 'EDIT', payload: original })}>
                                    Edit
                                </DropdownMenuItem>
                            ) : (
                                <DropdownMenuItem onClick={() => onRowAction({ type: 'DETAIL', payload: original })}>
                                    Detail
                                </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                                onClick={() => onRowAction({ type: 'SETTLEMENT_DETAIL', payload: original })}
                            >
                                Perubahan Settlement
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            }
            switch (String(status_id)) {
                case STATUS_TYPE.REJECTED:
                    return (
                        <IconButton onClick={() => onRowAction({ type: 'EDIT', payload: original })}>
                            <EditOutline />
                        </IconButton>
                    );
                default:
                    return (
                        <IconButton onClick={() => onRowAction({ type: 'DETAIL', payload: original })}>
                            <EyeOutline />
                        </IconButton>
                    );
            }
        },
    },
];

const BankAccount = props => {
    const {
        hideProgress,
        showProgress,
        isShowProgress,
        idCabang,
        filterBranch,
        actions: { outlet },
        accessId,
        userEmail,
        userStrap,
        businessProfile,
        idPermission,
    } = props;
    const [tableData, setTableData] = useState([]);
    const [tableIsLoading, setTableIsLoading] = useState(false);
    const [outletOptions, setOutletOptions] = useState([]);
    const [ewalletOptions, setEwalletOptions] = useState([]);
    const [bankOptions, setBankOptions] = useState([]);
    const [totalData, setTotalData] = useState(0);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [openBankAccountForm, setOpenBankAccountForm] = useState(false);
    const [openAccOption, setOpenAccOption] = useState(false);
    const [addSettlementAccountModal, setAddSettlementAccountModal] = useState({
        open: false,
        detail: null,
    });
    const [accOption, setAccOption] = useState('');
    const [selectedBank, setSelectedBank] = useState('');
    const [selectedAccountOption, setSelectedAccountOption] = useState('');
    const [accountOptions, setAccountOptions] = useState('');
    const [searchQuery, setSearchQuery] = useState('');
    const [pageIndex, setPageIndex] = useState();
    const [hasMoreItems, setHasMoreItems] = useState(true);
    const [isLoading, setIsLoading] = useState(true);
    const [selectedOutletOptions, setSelectedOutletOptions] = useState([]);
    const [marketplaceOptions, setMarketplaceOptions] = useState([]);
    const [selectedMarketplaceOptions, setSelectedMarketplaceOptions] = useState([]);
    const [selectedOutletEdit, setSelectedOutletEdit] = useState([]);
    const [selectedMarketplaceEdit, setSelectedMarketplaceEdit] = useState([]);
    const [openAddNewAcc, setOpenAddNewAcc] = useState(false);
    const [tableQuery, setTableQuery] = useState({
        limit: 10,
        page: 0,
        status: '',
        search: '',
        id_outlet: '',
        order: null,
        sort: null,
        type: 'rekening',
    });
    const { addToast } = React.useContext(ToastContext);
    const [detail, setDetail] = useState({});
    const [isIntegratedWebstore, setIsIntegratedWebstore] = useState(false);
    const [isIntegratedWallet, setIsIntegratedWallet] = useState(false);
    const [isShowSettlement, setIsShowSettlement] = useState(false);
    const { t } = useTranslation(['Pengaturan/AkunProfil/bankInformation', 'translation']);

    const getOutletOption = async () => {
        showProgress();
        const { getOutlet } = outlet;

        try {
            const options = await getOutlet();
            setOutletOptions(options.map(val => ({ name: val.cabang_name, id: val.id_cabang })));
        } catch (error) {
            console.error(error);
        } finally {
            hideProgress();
        }
    };

    const fetchIntegratedOutlet = async () => {
        showProgress();
        const { getOutlet } = outlet;

        const payload = {
            show_weborder_number: 1,
        };

        try {
            const options = await getOutlet(payload);
            setIsShowSettlement(options.some(val => val.ewallet_submission || val.weborder_number));
        } catch (error) {
            console.error(error);
        } finally {
            hideProgress();
        }
    };

    const fetchOutlet = async rekeningId => {
        showProgress();
        const dtEwallet = [];
        const dtMarketplace = [];
        const { getOutlet } = outlet;

        const payload = {
            profile_id: rekeningId,
            show_weborder_number: 1,
        };

        try {
            const options = await getOutlet(payload);
            setIsIntegratedWallet(options.some(val => val.ewallet_submission));
            setIsIntegratedWebstore(options.some(val => val.weborder_number));
            options.map(val => {
                const dt = { name: val.cabang_name, id: val.id_cabang };
                if (val.weborder_number) {
                    dtMarketplace.push(dt);
                }
                if (val.ewallet_submission) {
                    dtEwallet.push(dt);
                }
            });
            setMarketplaceOptions(dtMarketplace);
            setEwalletOptions(dtEwallet);
        } catch (error) {
            console.error(error);
        } finally {
            hideProgress();
        }
    };

    const fetchDetail = async param => {
        showProgress();
        const payload = param;
        try {
            const res = await getDetailSubmissionProfile(payload);
            const selectedOutlet = res.data.ewallet_settlement
                .filter(item => item.outlet_id)
                .map(item => item.outlet_id.toString());
            const selectedMarketplace = res.data.toko_online_settlement
                .filter(item => item.outlet_id)
                .map(item => item.outlet_id.toString());

            fetchOutlet(res.data.id);
            setSelectedOutletEdit(selectedOutlet);
            setSelectedMarketplaceEdit(selectedMarketplace);
            setDetail(res.data);
        } catch (e) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const fetchTable = async param => {
        setTableIsLoading(true);
        const { page, search, id_outlet, order, sort, limit, status, type } = param;
        const payload = {
            type,
            limit,
            page: page + 1,
            ...(status !== 1 && {
                status,
            }),
            ...(search && {
                search,
            }),
            ...(id_outlet && {
                id_outlet,
            }),
            ...(order && {
                order,
            }),
            ...(sort && {
                sort,
            }),
        };
        try {
            const res = await getListSubmissionProfile(payload);
            setTableData(res.data);
            setTotalData(res.meta.total);
        } catch (e) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            hideProgress();
            setTableIsLoading(false);
        }
    };

    const fetchingData = async ({ index }) => {
        if (!hasMoreItems && index === undefined) return;
        setIsLoading(true);
        const payload = {
            limit: 10,
            page: index !== undefined ? index : pageIndex,
            ...(searchQuery && {
                search: searchQuery,
            }),
            ...(accessId !== ROLES.ADMIN && {
                id_outlet: idCabang,
            }),
            type: 'rekening',
        };
        try {
            const optionsRes = await getListSubmissionProfile(payload);
            const optionRes = [];
            if (!optionsRes.data) return;
            optionsRes.data.map(item => {
                if (
                    String(item.status_id) === STATUS_TYPE.APPROVED ||
                    String(item.status_id) === STATUS_TYPE.INTEGRATED
                ) {
                    optionRes.push({
                        value: item.id,
                        name: `${item.account_no} - ${item.bank_name}`,
                    });
                }
            });

            setAccountOptions([...accountOptions, ...optionRes]);
            setPageIndex((optionsRes.meta.current_page || index) + 1);
            setHasMoreItems(optionsRes.meta.current_page < optionsRes.meta.total_page);
            setIsLoading(false);
        } catch (error) {
            console.error(error);
        } finally {
            setIsLoading(false);
        }
    };

    const fetchData = useCallback(fetchingData, [selectedOutletOptions, pageIndex, hasMoreItems]);

    const fetchBank = async () => {
        const res = await getBank();
        if (res.status && res.data) {
            const banks = res.data
                .filter(val => val.bank_name !== 'Lainnya')
                .map(val => {
                    const baseOpt = { value: val.id_bank, name: val.bank_name };
                    if (val.bank_name === 'Bank Raya') {
                        return {
                            ...baseOpt,
                            render: () => (
                                <Flex align="center" gap={3}>
                                    <Text>Bank Raya</Text>
                                    <Flex
                                        justify="center"
                                        align="center"
                                        css={{
                                            fontWeight: 600,
                                            backgroundColor: '#DF2A36',
                                            color: '#fff',
                                            borderRadius: '300px',
                                            height: '20px',
                                            padding: '0 10px',
                                            fontSize: '12px',
                                        }}
                                    >
                                        BETA
                                    </Flex>
                                </Flex>
                            ),
                        };
                    }

                    return baseOpt;
                });
            setBankOptions(banks);
        }
    };

    useEffect(() => {
        fetchTable({ ...tableQuery, id_outlet: filterBranch });
    }, [tableQuery, filterBranch]);

    useEffect(() => {
        if (idPermission === ROLES.ADMIN) {
            fetchIntegratedOutlet();
        }
        getOutletOption();
        fetchBank();
        fetchData({ index: 1 });
    }, []);

    const create = async param => {
        showProgress();
        setIsSubmitting(true);
        const { id, ...payload } = param;
        const modifiedPayload = {
            ...payload,
            status: payload.status ? 'active' : 'deactive',
        };

        try {
            const res = await createSubmissionProfile(modifiedPayload);
            if (res.status) {
                addToast({
                    title: t('toast.success', { ns: 'translation' }),
                    variant: 'success',
                    description: t('toast.success'),
                });
            } else {
                throw new Error();
            }
        } catch (e) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                variant: 'failed',
                description: t('toast.error'),
            });
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            setIsSubmitting(false);
            setOpenBankAccountForm(false);
            fetchTable({ ...tableQuery, id_outlet: filterBranch });
            hideProgress();
        }
    };

    const destroy = async param => {
        showProgress();
        setIsSubmitting(true);
        try {
            const res = await deleteSubmissionProfile(param.id);
            if (res.status) {
                addToast({
                    title: t('toast.success', { ns: 'translation' }),
                    variant: 'success',
                    description: t('toast.deleteSuccess'),
                });
            } else {
                throw new Error();
            }
        } catch (e) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                variant: 'failed',
                description: t('toast.deleteFailed'),
            });
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            setIsSubmitting(false);
            setOpenBankAccountForm(false);
            fetchTable({ ...tableQuery, id_outlet: filterBranch });
            hideProgress();
        }
    };

    const update = async param => {
        showProgress();
        setIsSubmitting(true);
        const payload = param;
        const payloadStatus = {
            id: param.id,
            status: param.status ? 'active' : 'deactive',
        };

        const settlementPayload = {
            id: payload.id,
            outlet_ids_ewallet: payload.ewallet_settlement,
            outlet_ids_toko_online: payload.toko_online_settlement,
        };

        const defaultActiveStatus = detail.is_active === undefined ? true : detail.is_active;
        const isActiveStatusChanged =
            String(detail.status_id) === STATUS_TYPE.APPROVED && defaultActiveStatus !== param.status;
        try {
            const res = await updateSubmissionProfile(payload);
            let resStatus = { msg: '', status: true };
            if (isActiveStatusChanged) resStatus = await changeStatusActiveRekening(payloadStatus);
            if (res.status || (isActiveStatusChanged && resStatus.status)) {
                await setSettlement(settlementPayload);
                addToast({
                    title: t('toast.success', { ns: 'translation' }),
                    variant: 'success',
                    description: t('toast.successUpdate'),
                });
            } else {
                throw new Error();
            }
        } catch (e) {
            if (typeof e.message === 'string' && e.message.split('|')[0] === 'URK-01') {
                try {
                    setIsSubmitting(true);

                    const payload = {
                        outlets: param.outlets,
                        id: param.id,
                    };

                    const res = await patchSubmissionProfile(JSON.stringify(payload));

                    if (res.status) {
                        return addToast({
                            title: t('toast.success', { ns: 'translation' }),
                            variant: 'success',
                            description: t('toast.successUpdate'),
                        });
                    }
                    throw new Error();
                } catch (err) {
                    addToast({
                        title: t('toast.error', { ns: 'translation' }),
                        variant: 'failed',
                        description: t('toast.failedUpdate'),
                    });
                    return addToast({
                        title: t('toast.error', { ns: 'translation' }),
                        description: catchError(err),
                        variant: 'failed',
                    });
                }
            }

            addToast({
                title: t('toast.error', { ns: 'translation' }),
                variant: 'failed',
                description: t('toast.failedUpdate'),
            });
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            setIsSubmitting(false);
            setOpenBankAccountForm(false);
            fetchTable({ ...tableQuery, id_outlet: filterBranch });
            hideProgress();
        }
    };

    const updateStatus = async param => {
        showProgress();
        setIsSubmitting(true);
        const payload = {
            id: param.id,
            status: param.status ? 'active' : 'deactive',
        };
        const payloadPayroll = {
            id: param.id,
            bank_id: param.bank_id,
            is_payroll_account: param.is_payroll_account,
        };

        try {
            const res = await changeStatusActiveRekening(payload);
            let resPayroll = { msg: '', status: true };
            if (String(param.bank_id) === BANK_ID.RAYA) resPayroll = await userRekeningPayrollStatus(payloadPayroll);
            if (res.status && resPayroll.status) {
                addToast({
                    title: t('toast.success', { ns: 'translation' }),
                    variant: 'success',
                    description: t('toast.successStatus'),
                });
            } else {
                throw new Error();
            }
        } catch (e) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                variant: 'failed',
                description: t('toast.failedStatus'),
            });
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            setIsSubmitting(false);
            setOpenBankAccountForm(false);
            fetchTable({ ...tableQuery, id_outlet: filterBranch });
            hideProgress();
        }
    };

    const handleSearch = useCallback(
        debounce(value => setTableQuery(query => ({ ...query, search: value })), 1000),
        [],
    );

    const onSubmit = data => {
        const { payload, type } = data;
        switch (String(type)) {
            case 'DELETE':
                destroy(payload);
                break;
            case STATUS_TYPE.APPROVED:
            case STATUS_TYPE.ON_PROCESS:
            case STATUS_TYPE.REJECTED:
                update(payload);
                break;
            case STATUS_TYPE.INTEGRATED:
                updateStatus(payload);
                break;
            default:
                create(payload);
                break;
        }
    };

    const handleRowAction = row => {
        switch (row.type) {
            case 'DETAIL':
            case 'EDIT':
                setAccOption(ACCOUNT_TYPE.EXISTING);
                setOpenBankAccountForm(true);
                if (!row) return;
                fetchDetail(row.payload.id);
                break;
            case 'SETTLEMENT_DETAIL':
                handleAddSettlement(row.payload);
                break;
            default:
                setAccOption(ACCOUNT_TYPE.EXISTING);
                setOpenBankAccountForm(true);
                if (!row) return;
                break;
        }
    };

    const handleAccOption = () => {
        if (selectedBank && +selectedBank.value === 154) {
            // 154: Bank Raya
            handleAnalyticLogEventBankRaya({ userEmail, userStrap });
        }

        setOpenBankAccountForm(true);
        setOpenAccOption(false);
    };

    const handleAccountSettlement = async () => {
        showProgress();
        const payload = {
            id: selectedAccountOption.value,
            outlet_ids_ewallet: selectedOutletOptions,
            outlet_ids_toko_online: selectedMarketplaceOptions,
        };

        try {
            await setSettlement(payload);
            addToast({
                title: t('toast.success', { ns: 'translation' }),
                variant: 'success',
                description: t('toast.settlementSuccess'),
            });
        } catch (error) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                variant: 'failed',
                description: t('toast.settlementError'),
            });
        } finally {
            setOpenAddSettlementAccount(false);
            fetchTable({ ...tableQuery, id_outlet: filterBranch });
            setSelectedAccountOption('');
            setSelectedOutletOptions([]);
            setSelectedMarketplaceOptions([]);
            hideProgress();
        }
    };

    const handleAddAccount = () => {
        handleAnalyticLogEventAddBankAccount({ userEmail, userStrap });
        setAccOption('');
        setSelectedBank('');
        setOpenAccOption(true);
    };

    const handleAddSettlement = rowData => {
        setAccOption('');
        setSelectedBank('');
        setAddSettlementAccountModal({
            open: true,
            detail: rowData && rowData.id ? rowData : null,
        });
    };

    return (
        <React.Fragment>
            <Paper
                responsive
                css={{
                    '@sm': { padding: 'unset' },
                    '@md': { padding: '20px' },
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '$compact',
                    mb: '$comfortable',
                }}
            >
                <TitleSection
                    title={t('titleSection.title', 'Informasi Rekening')}
                    onClickButton={handleAddAccount}
                    onClickAddSettlementAccountButton={handleAddSettlement}
                    isShowSettlement={isShowSettlement}
                    t={t}
                />
                <BannerText css={{ my: 0 }} />
                <Separator css={{ display: 'none', '@md': { display: 'block' } }} />
                <FilterSection
                    onAdd={handleAddAccount}
                    onAddSettlement={handleAddSettlement}
                    onChangeSearch={val => handleSearch(val)}
                    onChangeButtonGroup={val => setTableQuery({ ...tableQuery, status: val })}
                    isShowSettlement={isShowSettlement}
                    t={t}
                />
                <Separator />
                <Table
                    css={{ padding: 0 }}
                    data={tableData}
                    columns={headerData(row => handleRowAction(row), t)}
                    onRowClick={record => {
                        if (isNullOrUndefined(record.original.status_request_settlement_id)) {
                            handleRowAction({
                                payload: record.original,
                                type: record.original.status_id === STATUS_TYPE.REJECTED ? 'EDIT' : 'DETAIL',
                            });
                        }
                    }}
                    isLoading={tableIsLoading}
                    totalData={totalData}
                    fetchData={tq =>
                        setTableQuery(query => ({
                            ...query,
                            limit: tq.pageSize,
                            page: tq.pageIndex,
                            sort: tq.sortDirection,
                            order: tq.sortAccessor,
                        }))
                    }
                    customEmptyDataProps={{
                        doodleImage: <Image src={emptyData} width="200" height="auto" />,
                        title: (
                            <Paragraph paragraph="shortContentBold" color="primary" align="center">
                                {t('emptyMessage', 'Belum ada rekening')}
                            </Paragraph>
                        ),
                    }}
                    searchQuery={tableQuery.search}
                />
            </Paper>

            {openBankAccountForm && (
                <AddBankAccountForm
                    isMobile={isMobile.matches}
                    open={openBankAccountForm}
                    onOpenChange={setOpenBankAccountForm}
                    key={`${detail.id}-${openBankAccountForm}`}
                    onSubmit={payload => onSubmit(payload)}
                    defaultOutlet={idCabang}
                    outletOptions={outletOptions}
                    bankOptions={bankOptions}
                    detail={detail}
                    setDetail={setDetail}
                    type={accOption}
                    selectedBank={selectedBank}
                    selectedOutletEdit={selectedOutletEdit}
                    setSelectedOutletEdit={setSelectedOutletEdit}
                    selectedMarketplaceEdit={selectedMarketplaceEdit}
                    setSelectedMarketplaceEdit={setSelectedMarketplaceEdit}
                    marketplaceOptions={marketplaceOptions}
                    ewalletOptions={ewalletOptions}
                    accountOptions={accountOptions}
                    fetchData={fetchData}
                    isLoading={isLoading}
                    openInfoModal={() => setOpenAddNewAcc(true)}
                    isIntegratedWebstore={isIntegratedWebstore}
                    setIsIntegratedWebstore={setIsIntegratedWebstore}
                    isIntegratedWallet={isIntegratedWallet}
                    setIsIntegratedWallet={setIsIntegratedWallet}
                    businessProfile={businessProfile}
                    showProgress={showProgress}
                    hideProgress={hideProgress}
                    t={t}
                    outletId={filterBranch || idCabang}
                />
            )}

            {openAccOption && (
                <AccountOptionModal
                    open={openAccOption}
                    onOpenChange={setOpenAccOption}
                    onConfirm={handleAccOption}
                    accOption={accOption}
                    setAccOption={setAccOption}
                    selectedBank={selectedBank}
                    setSelectedBank={setSelectedBank}
                    t={t}
                />
            )}

            {addSettlementAccountModal.open && (
                <SettlementAccountModal
                    open={addSettlementAccountModal.open}
                    setOpen={open => {
                        setAddSettlementAccountModal(current => ({ ...current, open }));
                    }}
                    isMobile={isMobile.matches}
                    t={t}
                    showProgress={showProgress}
                    hideProgress={hideProgress}
                    isShowProgress={isShowProgress}
                    businessProfile={businessProfile}
                    detail={addSettlementAccountModal.detail}
                    onSuccess={() => {
                        fetchTable({ ...tableQuery, id_outlet: filterBranch });
                    }}
                    outletId={filterBranch || idCabang}
                />
            )}

            <ConfirmAddNewAcc
                open={openAddNewAcc}
                onCancel={() => {
                    setOpenAddNewAcc(false);
                    setOpenBankAccountForm(false);
                }}
                onClose={() => {
                    setOpenAddNewAcc(false);
                }}
                onConfirm={() => {
                    setAccOption(ACCOUNT_TYPE.EXISTING);
                    setOpenAddNewAcc(false);
                }}
                t={t}
            />
        </React.Fragment>
    );
};

BankAccount.propTypes = {
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
    actions: PropTypes.shape({
        outlet: PropTypes.shape({
            getOutlet: PropTypes.func,
        }),
    }).isRequired,
    filterBranch: PropTypes.string.isRequired,
    idPermission: PropTypes.string,
};

BankAccount.defaultProps = {
    showProgress: () => {},
    hideProgress: () => {},
    idPermission: '',
};

const mapStateToProps = state => ({
    account: state.accountInfo.accountInfoResult,
    isFetching: state.accountInfo.isFetching,
    accessId: state && state.accountInfo.accountInfoResult.hak_akses_id,
    userEmail: state.accountInfo.accountInfoResult.user_email,
    userStrap: state.users.strap,
    businessProfile: {
        ownerPhone: state.user.profile.user_usaha_notlp,
        ownerEmail: state.user.profile.user_usaha_email,
    },
});

const mapDispatchToProps = dispatch => ({
    actions: {
        outlet: bindActionCreators(outletActions, dispatch),
    },
});

export default CoreHOC(connect(mapStateToProps, mapDispatchToProps)(BankAccount));
