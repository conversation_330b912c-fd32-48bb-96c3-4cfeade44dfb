import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import * as PropTypes from 'prop-types';
import {
    ModalDialog,
    ModalDialogTitle,
    ModalDialogContent,
    ModalDialogFooter,
    Text,
    Flex,
    FormGroup,
    FormLabel,
    CheckboxGroup,
    InputCheckbox,
    InputSelectTag,
    InputSelect,
    Button,
    FormHelper,
    Banner,
    BannerDescription,
    Box,
    ToastContext,
    IconButton,
    Paragraph,
    Paper,
    AlertDialog,
    TagStatus,
} from '@majoo-ui/react';
import { UploadOutline, CircleInfoFilled, CloseFilled, TriangleExclamationFilled } from '@majoo-ui/icons';
import { useForm, Controller } from 'react-hook-form';

import {
    getIntegrationMerchantStatus,
    getBranchByIntegration,
    getAccountsByMultiOutlet,
    postSettlementUpdateRequest,
    getSettlementUpdateRequest,
    checkFile,
    getAccountInfo,
} from '../../../../../data/users';
import { uploadImage } from '~/data/upload';
import { catchError } from '../../../../../utils/helper';
import * as employeeApi from '../../../../../data/employee';
import AuthorizationModal from '../../AccountInfo/components/authorizationModal';
import VerificationModal from '../../AccountInfo/components/verificationModal';
import { colors } from '~/stitches.config';
import { STATUS_TYPE } from '../constants';

const getStatusBadge = (t) => ({
    [STATUS_TYPE.ON_PROCESS]: {
        label: t('settlementAccount.status.onProcess', 'Perubahan Diproses'),
        tagType: 'process',
    },
    [STATUS_TYPE.APPROVED]: {
        label: t('settlementAccount.status.approved', 'Perubahan Disetujui'),
        tagType: 'success',
    },
    [STATUS_TYPE.REJECTED]: {
        label: t('settlementAccount.status.rejected', 'Perubahan Ditolak'),
        tagType: 'error',
    },
});

const getStatusDefaultDescription = (t) => ({
    [STATUS_TYPE.ON_PROCESS]: t('settlementAccount.statusDescription.onProcess', 'Pengajuan perubahan rekening settlement sedang diproses.'),
    [STATUS_TYPE.APPROVED]: t('settlementAccount.statusDescription.approved', 'Perubahan rekening settlement telah disetujui.'),
    [STATUS_TYPE.REJECTED]: t('settlementAccount.statusDescription.rejected', 'Foto yang dilampirkan tidak sesuai. Silakan unggah halaman pertama buku tabungan atau QR Code yang tertera nomor dan nama pemilik rekening.'),
});

const getFileNameFromPath = path => {
    if (!path || typeof path !== 'string') return '';
    const segments = path.split('/');
    return segments[segments.length - 1] || path;
};

const SettlementAccountModal = ({
    open,
    setOpen,
    isMobile,
    t,
    showProgress,
    hideProgress,
    isShowProgress,
    onSuccess,
    onDownloadTemplate,
    businessProfile,
    detail,
    outletId,
}) => {
    const { addToast } = React.useContext(ToastContext);
    const [integrationAvailability, setIntegrationAvailability] = useState({ qris: false, toko_online: false });
    const [outletOptions, setOutletOptions] = useState([]);
    const [accountOptions, setAccountOptions] = useState([]);
    const [statementFiles, setStatementFiles] = useState([]);
    const [isFetchingOutlets, setIsFetchingOutlets] = useState(false);
    const [isFetchingAccounts, setIsFetchingAccounts] = useState(false);
    const [isUploadingStatement, setIsUploadingStatement] = useState(false);
    const [openAuthorizationModal, setOpenAuthorizationModal] = useState(false);
    const [verificationModal, setVerificationModal] = useState({
        open: false,
        type: 'phone',
        isAuth: false,
        contactValue: '',
    });
    const [userData, setUserData] = useState({
        email: businessProfile.ownerEmail,
        phone: businessProfile.ownerPhone,
    });
    const [pendingPayload, setPendingPayload] = useState(null);
    const [alertModal, setAlertModal] = useState({
        open: false,
        title: '',
        description: '',
        onConfirm: () => {},
    });
    const statementInputRef = useRef(null);
    const [detailData, setDetailData] = useState(null);
    const [isDetailLoading, setIsDetailLoading] = useState(false);

    const isDetailMode = !!detail;

    const defaultValues = useMemo(
        () => ({
            settlement_type: [],
            outlet_ids: [],
            rekening_id: '',
            berkas_file_pernyataan: '',
        }),
        [],
    );

    const {
        control,
        handleSubmit,
        watch,
        setValue,
        getValues,
        clearErrors,
        setError,
        formState: { errors, isSubmitting },
    } = useForm({
        defaultValues,
    });

    const settlementTypes = watch('settlement_type');
    const selectedOutlets = watch('outlet_ids');
    const selectedRekeningId = watch('rekening_id');
    const statementUrl = watch('berkas_file_pernyataan');

    const statusMeta = useMemo(() => {
        if (!isDetailMode) return null;
        const statusId = String(detail?.status_request_settlement_id || '');
        const STATUS_BADGE = getStatusBadge(t);
        const baseMeta = STATUS_BADGE[statusId] || {
            label: t('settlementAccount.status.default', 'Perubahan Settlement'),
            tagType: 'info',
        };

        const STATUS_DEFAULT_DESCRIPTION = getStatusDefaultDescription(t);
        const description =
            detailData?.status_note ||
            detailData?.status_description ||
            detailData?.note ||
            detail?.status_request_settlement_note ||
            detail?.status_request_settlement_description ||
            (statusId === STATUS_TYPE.REJECTED
                ? t('info.rejected', STATUS_DEFAULT_DESCRIPTION[STATUS_TYPE.REJECTED])
                : '') ||
            STATUS_DEFAULT_DESCRIPTION[statusId] ||
            '';

        return {
            ...baseMeta,
            description,
        };
    }, [detail, detailData, isDetailMode, t]);

    const showErrorToast = useCallback(
        error => {
            addToast({
                title: t('toast.error', { ns: 'translation', defaultValue: 'Gagal' }),
                description: catchError(error),
                variant: 'failed',
            });
        },
        [addToast, t],
    );

    const showSuccessToast = useCallback(() => {
        addToast({
            title: t('toast.success', { ns: 'translation', defaultValue: 'Berhasil' }),
            description: t('settlementAccount.successMessage', 'Perubahan pengaturan rekening settlement berhasil disimpan'),
            variant: 'success',
        });
    }, [addToast, t]);

    const fetchUserData = useCallback(async () => {
        try {
            const response = await getAccountInfo();
            if (response.status) {
                const { data } = response;
                setUserData(current => ({
                    email: current.email || data.user_email || '',
                    phone: current.phone || data.user_notlp || '',
                }));
            }
        } catch (error) {
            showErrorToast(error);
        }
    }, [showErrorToast]);

    const fetchRequestVerifCode = useCallback(
        async (contactValue, verificationType, onSuccess, onError) => {
            showProgress();
            const verificationPayload = {
                type: 'update_rekening_settlement',
                value: getValues('rekening_id'),
                send_to: verificationType,
            };

            try {
                await employeeApi.requestVerificationCode(verificationPayload);
                if (onSuccess) onSuccess();
                setVerificationModal({ open: true, type: verificationType, contactValue });
                setOpenAuthorizationModal(false);
            } catch (error) {
                if (onError) onError(error);
                showErrorToast(error);
            } finally {
                hideProgress();
            }
        },
        [getValues, hideProgress, showErrorToast, showProgress],
    );

    const submitSettlementRequest = useCallback(async payload => {
        showProgress();
        try {
            await postSettlementUpdateRequest(payload);
            showSuccessToast();
            if (onSuccess) onSuccess();
            setOpen(false);
        } catch (error) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    }, []);

    const fetchVerifyData = useCallback(
        async (code, verificationType, onError) => {
            showProgress();
            try {
                await employeeApi.verifyCode({
                    type: 'update_rekening_settlement',
                    code,
                });

                setVerificationModal({ open: false, type: verificationType, isAuth: false });

                if (pendingPayload) {
                    setAlertModal({
                        open: true,
                        title: t('settlementAccount.saveTitle', 'Simpan Rekening Settlement'),
                        description: t('settlementAccount.saveDescription', 'Pengaturan rekening settlement akan disimpan dan tampil di daftar rekening sesuai dengan pengaturan yang dilakukan. Lanjutkan?'),
                        onConfirm: () => submitSettlementRequest(pendingPayload),
                    });
                }
            } catch (error) {
                if (onError) onError(error);
                showErrorToast(error);
            } finally {
                hideProgress();
            }
        },
        [pendingPayload],
    );

    const handleOpenChange = value => {
        setOpen(value);
    };

    const handleStatementChange = async event => {
        if (isDetailMode) return;
        const inputElement = event?.target || null;
        const files = inputElement?.files ? Array.from(inputElement.files) : [];

        if (!files.length) {
            setStatementFiles([]);
            setValue('berkas_file_pernyataan', '');
            return;
        }

        try {
            setIsUploadingStatement(true);
            const [file] = files;
            const blob = file;
            const formData = new FormData();
            formData.append('userfile', blob, blob?.name || file?.name || 'statement');

            const response = await uploadImage(formData);
            if (!response?.item_image_path) {
                throw new Error(response?.msg || t('settlementAccount.uploadFailed', 'Upload failed'));
            }

            const uploadedFile = {
                url: response.item_image_path,
                name: file?.name || blob?.name || 'statement',
            };

            setStatementFiles([uploadedFile]);
            setValue('berkas_file_pernyataan', response.item_image_path);
            clearErrors('berkas_file_pernyataan');
        } catch (error) {
            setStatementFiles([]);
            setValue('berkas_file_pernyataan', '');
            showErrorToast(error);
        } finally {
            setIsUploadingStatement(false);
            if (inputElement) {
                inputElement.value = '';
            }
        }
    };

    const handleStatementRemove = () => {
        if (isDetailMode) return;
        setStatementFiles([]);
        setValue('berkas_file_pernyataan', '');
        setError('berkas_file_pernyataan', {
            type: 'manual',
            message: t('settlementAccount.statementRequired', 'Unggah surat pernyataan terlebih dahulu'),
        });
        if (statementInputRef.current) {
            statementInputRef.current.value = '';
        }
    };

    const handleFormSubmit = async values => {
        if (isDetailMode) return;
        if (!values.berkas_file_pernyataan) {
            setError('berkas_file_pernyataan', {
                type: 'manual',
                message: t('settlementAccount.statementRequired', 'Unggah surat pernyataan terlebih dahulu'),
            });
            return;
        }

        const outletIds = values.outlet_ids.map(id => {
            const parsed = Number(id);
            return Number.isNaN(parsed) ? id : parsed;
        });

        const payload = {
            outlet_ids: outletIds,
            rekening_id: values.rekening_id,
            settlement_type: values.settlement_type,
            berkas_file_pernyataan: values.berkas_file_pernyataan,
        };

        setPendingPayload(payload);

        if (!userData.email || !userData.phone) {
            await fetchUserData();
        }

        setOpenAuthorizationModal(true);
    };

    const fetchDownloadTemplate = async () => {
        if (onDownloadTemplate) {
            onDownloadTemplate();
            return;
        }
        try {
            const response = await checkFile({
                file_name: 'TemplatePernyataanSettlement.docx',
            });
            window.open(response.file_path, '_self');
        } catch (e) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
                position: 'top-right',
            });
        }
    };

    const isSubmitDisabled =
        isSubmitting ||
        isUploadingStatement ||
        !settlementTypes?.length ||
        !selectedOutlets?.length ||
        !selectedRekeningId ||
        !statementUrl;

    useEffect(() => {
        if (!isDetailMode) {
            setDetailData(null);
            return;
        }

        const loadDetailData = async () => {
            setIsDetailLoading(true);
            try {
                showProgress();
                const response = await getSettlementUpdateRequest(detail.id);
                const requestDetail = response?.data || {};
                setDetailData(requestDetail);

                const settledTypesRaw = requestDetail?.settlement_type;
                const settledTypes = Array.isArray(settledTypesRaw)
                    ? settledTypesRaw
                    : typeof settledTypesRaw === 'string'
                    ? settledTypesRaw
                          .split(',')
                          .map(item => item.trim())
                          .filter(Boolean)
                    : [];
                setValue('settlement_type', settledTypes);

                const outletsSource =
                    [requestDetail?.outlets, requestDetail?.outlet_list, requestDetail?.outlet_detail].find(
                        Array.isArray,
                    ) || [];

                const formattedOutlets = outletsSource
                    .map(item => ({
                        id: String(item?.id || item?.outlet_id || item?.id_cabang || item?.branch_id || ''),
                        name: item?.name || item?.outlet_name || item?.cabang_name || item?.branch_name || '',
                    }))
                    .filter(item => item.id && item.name);

                if (formattedOutlets.length) {
                    setOutletOptions(formattedOutlets);
                }

                const fallbackOutlets = !formattedOutlets.length
                    ? [...(detail?.ewallet_settlement || []), ...(detail?.toko_online_settlement || [])]
                          .map(item => ({
                              id: String(item?.outlet_id || item?.id_cabang || ''),
                              name: item?.outlet_name || item?.name || '',
                          }))
                          .filter(item => item.id && item.name)
                    : [];

                if (fallbackOutlets.length) {
                    setOutletOptions(fallbackOutlets);
                }

                const outletIds = Array.isArray(requestDetail?.outlet_ids)
                    ? requestDetail.outlet_ids.map(id => String(id))
                    : (formattedOutlets.length ? formattedOutlets : fallbackOutlets).map(option => option.id);
                setValue('outlet_ids', outletIds);

                const rekeningSource =
                    requestDetail?.rekening || requestDetail?.account || requestDetail?.rekening_info || {};

                const rekeningOption = {
                    value: String(
                        rekeningSource?.rekening_id || rekeningSource?.id || detail?.rekening_id || detail?.id || '',
                    ),
                    name: `${
                        rekeningSource?.bank_name || requestDetail?.rekening_bank_name || detail?.bank_name || ''
                    }-${rekeningSource?.rekening_no || rekeningSource?.account_no || detail?.account_no || ''}`,
                };

                if (rekeningOption.value && rekeningOption.name !== '-') {
                    setAccountOptions([rekeningOption]);
                    setValue('rekening_id', rekeningOption.value);
                }

                if (!rekeningOption.value) {
                    const fallbackValue = String(detail?.rekening_id || detail?.id || '');
                    const fallbackName =
                        detail?.bank_name && detail?.account_no
                            ? `${detail.bank_name}-${detail.account_no}`
                            : detail?.account_no || '';

                    if (fallbackValue) {
                        setAccountOptions([
                            {
                                value: fallbackValue,
                                name: fallbackName || fallbackValue,
                            },
                        ]);
                        setValue('rekening_id', fallbackValue);
                    }
                }

                if (requestDetail?.berkas_file_pernyataan) {
                    setStatementFiles([
                        {
                            url: requestDetail.berkas_file_pernyataan,
                            name:
                                requestDetail?.berkas_file_pernyataan_name ||
                                requestDetail?.berkas_file_pernyataan_original_name ||
                                getFileNameFromPath(requestDetail.berkas_file_pernyataan),
                        },
                    ]);
                    setValue('berkas_file_pernyataan', requestDetail.berkas_file_pernyataan);
                } else if (detail?.berkas_file_pernyataan) {
                    setStatementFiles([
                        {
                            url: detail.berkas_file_pernyataan,
                            name: getFileNameFromPath(detail.berkas_file_pernyataan),
                        },
                    ]);
                    setValue('berkas_file_pernyataan', detail.berkas_file_pernyataan);
                }
            } catch (error) {
                showErrorToast(error);
            } finally {
                setIsDetailLoading(false);
                hideProgress();
            }
        };

        loadDetailData();
    }, [detail]);

    useEffect(() => {
        if (!open || isDetailMode) return;

        const loadIntegrationStatus = async () => {
            try {
                showProgress();
                const response = await getIntegrationMerchantStatus();
                const data = response?.data || {};
                const availability = {
                    qris: data.qris_status === 1,
                    toko_online: data.weborder_status === 1,
                };
                const activeTypes = [];

                setIntegrationAvailability(availability);
                setValue('settlement_type', activeTypes);
            } catch (error) {
                showErrorToast(error);
            } finally {
                hideProgress();
            }
        };

        loadIntegrationStatus();
    }, []);

    useEffect(() => {
        if (!open || isDetailMode) return;
        if (!settlementTypes || settlementTypes.length === 0) {
            setOutletOptions([]);
            setAccountOptions([]);
            if (getValues('outlet_ids').length) setValue('outlet_ids', []);
            if (getValues('rekening_id')) setValue('rekening_id', '');
            return;
        }

        const fetchOutlets = async () => {
            setIsFetchingOutlets(true);
            try {
                const integrationQuery = settlementTypes.join(',');
                const response = await getBranchByIntegration(integrationQuery);
                const options = (response?.data || []).map(item => ({
                    id: String(item.id_cabang),
                    name: item.cabang_name,
                }));
                setOutletOptions(options);

                const currentSelection = getValues('outlet_ids');
                if (currentSelection.length) {
                    const allowedIds = new Set(options.map(option => option.id));
                    const filteredSelection = currentSelection.filter(id => allowedIds.has(id));
                    if (filteredSelection.length !== currentSelection.length) {
                        setValue('outlet_ids', filteredSelection);
                    }
                }
            } catch (error) {
                showErrorToast(error);
            } finally {
                setIsFetchingOutlets(false);
            }
        };

        fetchOutlets();
    }, [getValues, isDetailMode, open, settlementTypes, setValue, showErrorToast]);

    useEffect(() => {
        if (!open || isDetailMode) return;
        if (!selectedOutlets || selectedOutlets.length === 0) {
            setAccountOptions([]);
            if (getValues('rekening_id')) setValue('rekening_id', '');
            return;
        }

        const fetchAccounts = async () => {
            showProgress();
            setIsFetchingAccounts(true);
            try {
                const response = await getAccountsByMultiOutlet(selectedOutlets.join(','));
                const options = (response?.data || []).map(item => ({
                    value: item.rekening_id,
                    name: `${item.rekening_bank_name}-${item.rekening_no}`,
                    isDisabled: String(item.status_request_settlement_id) === STATUS_TYPE.ON_PROCESS,
                    render: () => (
                        <Flex direction="column">
                            <Text>
                                {item.rekening_bank_name}-{item.rekening_no}
                            </Text>
                            {String(item.status_request_settlement_id) === STATUS_TYPE.ON_PROCESS && (
                                <Text>{t('settlementAccount.processSettlementChange', 'Proses Perubahan Settlement')}</Text>
                            )}
                        </Flex>
                    ),
                }));
                setAccountOptions(options);

                const current = getValues('rekening_id');
                if (current && !options.some(option => option.value === current)) {
                    setValue('rekening_id', '');
                }
            } catch (error) {
                showErrorToast(error);
            } finally {
                setIsFetchingAccounts(false);
                hideProgress();
            }
        };

        fetchAccounts();
    }, [getValues, hideProgress, isDetailMode, open, selectedOutlets, setValue, showErrorToast, showProgress]);

    return (
        <React.Fragment>
            <ModalDialog size="md" isMobile={isMobile} open={open} onOpenChange={handleOpenChange}>
                <ModalDialogTitle>
                    {isDetailMode
                        ? t('settlementAccount.detailTitle', 'Rekening Settlement')
                        : t('settlementAccount.title', 'Atur Rekening Settlement')}
                </ModalDialogTitle>
                <ModalDialogContent
                    css={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '$spacing-05',
                        maxHeight: isMobile ? '80vh' : '70vh',
                        overflowY: 'auto',
                    }}
                >
                    {isDetailMode && statusMeta && (
                        <Flex direction="column" align="start" gap={5}>
                            <TagStatus type={statusMeta.tagType} css={{ maxWidth: 'unset' }}>
                                {statusMeta.label}
                            </TagStatus>
                            {statusMeta.description &&
                            String(detail.status_request_settlement_id) === STATUS_TYPE.REJECTED ? (
                                <Paragraph paragraph="longContentRegular" color="primary">
                                    {statusMeta.description}
                                </Paragraph>
                            ) : null}
                        </Flex>
                    )}
                    {(!isDetailMode || String(detail.status_request_settlement_id) === STATUS_TYPE.ON_PROCESS) && (
                        <Banner variant="info" css={{ marginBottom: '$spacing-04' }}>
                            <Flex gap={3} align="start">
                                <CircleInfoFilled />
                                <BannerDescription css={{ whiteSpace: 'unset' }}>
                                    {t(
                                        'settlementAccount.processingInfo',
                                        'Pengajuan atur rekening settlement membutuhkan waktu 2-3 hari kerja',
                                    )}
                                </BannerDescription>
                            </Flex>
                        </Banner>
                    )}
                    <Flex direction="column" gap={6}>
                        <FormGroup>
                            <FormLabel css={{ color: '$textPrimary' }}>
                                {t('settlementAccount.settlementTypeLabel', 'Jenis Settlement')}
                            </FormLabel>
                            <Controller
                                name="settlement_type"
                                control={control}
                                rules={{
                                    validate: value =>
                                        (value && value.length > 0) ||
                                        t(
                                            'settlementAccount.settlementTypeRequired',
                                            'Pilih minimal satu jenis settlement',
                                        ),
                                }}
                                render={({ field }) => (
                                    <CheckboxGroup
                                        key={JSON.stringify(field.value)}
                                        value={field.value}
                                        onChange={value => {
                                            if (isDetailMode) return;
                                            field.onChange(value);
                                            clearErrors('settlement_type');
                                        }}
                                    >
                                        <Flex align="center" css={{ width: '100%', '> div': { flex: 1 } }}>
                                            <InputCheckbox
                                                value="qris"
                                                label="QRIS"
                                                disabled={!integrationAvailability.qris || isDetailMode}
                                            />
                                            <InputCheckbox
                                                value="toko online"
                                                label={t('settlementAccount.onlineStoreOption', 'Toko Online')}
                                                disabled={!integrationAvailability.toko_online || isDetailMode}
                                            />
                                        </Flex>
                                    </CheckboxGroup>
                                )}
                            />
                            {errors.settlement_type && <FormHelper error>{errors.settlement_type.message}</FormHelper>}
                        </FormGroup>
                        <FormGroup>
                            <FormLabel css={{ color: '$textPrimary' }}>
                                {t('settlementAccount.outletLabel', 'Outlet')}
                            </FormLabel>
                            <Controller
                                name="outlet_ids"
                                control={control}
                                rules={{
                                    validate: value =>
                                        (value && value.length > 0) ||
                                        t('settlementAccount.outletRequired', 'Pilih outlet tujuan settlement'),
                                }}
                                render={({ field }) => (
                                    <InputSelectTag
                                        id="settlement-outlet"
                                        placeholder={t('settlementAccount.outletPlaceholder', 'Pilih outlet')}
                                        option={outletOptions}
                                        value={field.value}
                                        onChange={value => {
                                            field.onChange(value);
                                            clearErrors('outlet_ids');
                                        }}
                                        labelCounter={t('settlementAccount.dataSelected', 'Data dipilih')}
                                        isInvalid={!!errors.outlet_ids}
                                        isLoading={isFetchingOutlets}
                                        disabled={!settlementTypes.length || isDetailMode}
                                        css={{ width: '100%' }}
                                    />
                                )}
                            />
                            {errors.outlet_ids && <FormHelper error>{errors.outlet_ids.message}</FormHelper>}
                        </FormGroup>
                        {accountOptions.length > 0 || !selectedOutlets.length || isShowProgress ? (
                            <FormGroup>
                                <FormLabel css={{ color: '$textPrimary' }}>
                                    {t('settlementAccount.accountLabel', 'Rekening')}
                                </FormLabel>
                                <Controller
                                    name="rekening_id"
                                    control={control}
                                    rules={{
                                        required: t('settlementAccount.accountRequired', 'Pilih rekening settlement'),
                                    }}
                                    render={({ field }) => {
                                        const selectedOption =
                                            accountOptions.find(option => option.value === field.value) || '';
                                        return (
                                            <InputSelect
                                                id="rekening-settlement"
                                                placeholder={t(
                                                    'settlementAccount.accountPlaceholder',
                                                    'Pilih rekening settlement',
                                                )}
                                                option={accountOptions}
                                                value={selectedOption || undefined}
                                                onChange={option => {
                                                    field.onChange(option ? option.value : '');
                                                    clearErrors('rekening_id');
                                                }}
                                                isInvalid={!!errors.rekening_id}
                                                isLoading={isFetchingAccounts}
                                                disabled={!selectedOutlets.length || isDetailMode || isDetailLoading}
                                                css={{ width: '100%' }}
                                            />
                                        );
                                    }}
                                />
                                {errors.rekening_id && <FormHelper error>{errors.rekening_id.message}</FormHelper>}
                            </FormGroup>
                        ) : (
                            <Paper css={{ padding: '$spacing-03 $spacing-05' }}>
                                <Flex gap={2} align="start">
                                    <Box css={{ flex: 1 }}>
                                        <TriangleExclamationFilled color={colors.iconRed} />
                                    </Box>
                                    <Paragraph color="primary">
                                        {t(
                                            'settlementAccount.noAccountMatch',
                                            'Tidak ada rekening sesuai dengan kriteria outlet dipilih. Pastikan rekening yang diatur sesuai semua Outlet dipilih',
                                        )}
                                    </Paragraph>
                                </Flex>
                            </Paper>
                        )}
                        <FormGroup>
                            <FormLabel css={{ color: '$textPrimary' }}>
                                {t('settlementAccount.statementLabel', 'Surat Pernyataan')}
                            </FormLabel>
                            <Box>
                                <input
                                    id="settlement-statement"
                                    ref={statementInputRef}
                                    type="file"
                                    accept=".docx"
                                    onChange={handleStatementChange}
                                    disabled={isUploadingStatement || isDetailMode || isDetailLoading}
                                    style={{ display: 'none' }}
                                />
                                <label
                                    htmlFor="settlement-statement"
                                    style={{
                                        width: '100%',
                                        display: 'block',
                                        pointerEvents:
                                            isUploadingStatement || isDetailMode || isDetailLoading ? 'none' : 'auto',
                                    }}
                                >
                                    {statementFiles.length > 0 ? (
                                        <Flex
                                            align="center"
                                            gap={3}
                                            css={{
                                                width: '100%',
                                                borderRadius: '12px',
                                                padding: '$spacing-04',
                                                height: 56,
                                                justifyContent: 'start',
                                                backgroundColor: '$bgGray',
                                                border: '2px dashed $gray300',
                                                cursor:
                                                    isUploadingStatement || isDetailMode || isDetailLoading
                                                        ? 'not-allowed'
                                                        : 'pointer',
                                            }}
                                        >
                                            <UploadOutline />
                                            <Flex direction="column" css={{ flex: 1, width: '100%' }}>
                                                <Text variant="helper" color="primary">
                                                    {statementFiles[0].name}
                                                </Text>
                                                {!isDetailMode && (
                                                    <Text variant="helper" color="green">
                                                        {t('settlementAccount.changeFile', 'Ubah Berkas')}
                                                    </Text>
                                                )}
                                            </Flex>
                                            {!isDetailMode && (
                                                <IconButton
                                                    type="button"
                                                    onClick={e => {
                                                        e.preventDefault();
                                                        handleStatementRemove();
                                                    }}
                                                    css={{ padding: 0 }}
                                                >
                                                    <CloseFilled />
                                                </IconButton>
                                            )}
                                        </Flex>
                                    ) : (
                                        <Flex
                                            align="center"
                                            gap={3}
                                            css={{
                                                width: '100%',
                                                borderRadius: '12px',
                                                padding: '$spacing-04',
                                                height: 56,
                                                justifyContent: 'start',
                                                backgroundColor: '$bgGray',
                                                border: '2px dashed $gray300',
                                                cursor:
                                                    isUploadingStatement || isDetailMode || isDetailLoading
                                                        ? 'not-allowed'
                                                        : 'pointer',
                                            }}
                                        >
                                            <UploadOutline />
                                            <Text variant="caption" color="secondary" css={{ textAlign: 'center' }}>
                                                {t(
                                                    'settlementAccount.uploadPlaceholder',
                                                    'Pilih atau letakkan berkas di sini',
                                                )}
                                            </Text>
                                        </Flex>
                                    )}
                                </label>
                            </Box>
                            {errors.berkas_file_pernyataan && (
                                <FormHelper error>{errors.berkas_file_pernyataan.message}</FormHelper>
                            )}
                            {!isDetailMode && (
                                <Text variant="helper">
                                    {t('settlementAccount.noStatement', 'Belum memiliki surat pernyataan?')}{' '}
                                    <span
                                        style={{ color: '#00A99D', cursor: 'pointer' }}
                                        onClick={fetchDownloadTemplate}
                                    >
                                        {t('settlementAccount.downloadTemplate', 'Unduh template')}
                                    </span>
                                </Text>
                            )}
                        </FormGroup>
                    </Flex>
                </ModalDialogContent>
                <ModalDialogFooter
                    css={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        gap: '$spacing-02',
                    }}
                >
                    <Button buttonType="ghost" onClick={() => handleOpenChange(false)}>
                        {isDetailMode
                            ? t('label.close', { ns: 'translation', defaultValue: 'Tutup' })
                            : t('label.cancel', { ns: 'translation', defaultValue: 'Batal' })}
                    </Button>
                    {!isDetailMode && (
                        <Button onClick={handleSubmit(handleFormSubmit)} disabled={isSubmitDisabled || isDetailLoading}>
                            {t('label.save', { ns: 'translation', defaultValue: 'Simpan' })}
                        </Button>
                    )}
                </ModalDialogFooter>
            </ModalDialog>
            {openAuthorizationModal && (
                <AuthorizationModal
                    title={t('settlementAccount.ownerVerificationTitle', 'Verifikasi Rekening Owner')}
                    isOpen={openAuthorizationModal}
                    onOpenChange={setOpenAuthorizationModal}
                    phoneNumber={userData.phone}
                    email={userData.email}
                    requestVerification={(contactValue, verificationType) =>
                        fetchRequestVerifCode(contactValue, verificationType)
                    }
                    isMobile={isMobile}
                    t={t}
                    toOwner
                    outletId={outletId}
                />
            )}
            {verificationModal.open && (
                <VerificationModal
                    isMobile={isMobile}
                    isOpen={verificationModal.open}
                    onOpenChange={isOpen => setVerificationModal(current => ({ ...current, open: isOpen }))}
                    verificationType={verificationModal.type}
                    contactValue={
                        verificationModal.contactValue ||
                        (verificationModal.type === 'email' ? userData.email : userData.phone)
                    }
                    requestVerificationCode={(verificationType, contactValue, onSuccess, onError) =>
                        fetchRequestVerifCode(contactValue, verificationType, onSuccess, onError)
                    }
                    onSubmit={(code, verificationType, onError) => fetchVerifyData(code, verificationType, onError)}
                    t={t}
                />
            )}
            {alertModal.open && (
                <AlertDialog
                    open={alertModal.open}
                    onCancel={() => {
                        setAlertModal({ open: false });
                    }}
                    isMobile={isMobile}
                    onConfirm={alertModal.onConfirm}
                    description={alertModal.description}
                    title={alertModal.title}
                    labelConfirm={t('label.continue', { ns: 'translation' })}
                    labelCancel={t('label.cancel', { ns: 'translation' })}
                    dialogType="primary"
                    css={{
                        width: isMobile ? 'unset' : '422px',
                    }}
                />
            )}
        </React.Fragment>
    );
};

SettlementAccountModal.propTypes = {
    open: PropTypes.bool.isRequired,
    setOpen: PropTypes.func.isRequired,
    isMobile: PropTypes.bool,
    t: PropTypes.func,
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
    isShowProgress: PropTypes.bool,
    onSuccess: PropTypes.func,
    onDownloadTemplate: PropTypes.func,
    businessProfile: PropTypes.shape({
        ownerPhone: PropTypes.string,
        ownerEmail: PropTypes.string,
    }),
    detail: PropTypes.shape({}),
    outletId: PropTypes.string,
};

SettlementAccountModal.defaultProps = {
    isMobile: false,
    t: (_, val) => val,
    showProgress: () => {},
    hideProgress: () => {},
    isShowProgress: false,
    onSuccess: () => {},
    onDownloadTemplate: null,
    businessProfile: {
        ownerPhone: '',
        ownerEmail: '',
    },
    detail: null,
    outletId: '',
};

export default SettlementAccountModal;
