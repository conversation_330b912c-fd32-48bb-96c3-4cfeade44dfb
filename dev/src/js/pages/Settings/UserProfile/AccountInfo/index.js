import React, { useState, useEffect, useContext } from 'react';
import { connect } from 'react-redux';
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { Box, Flex, Paper, Button, Heading, Separator, AlertDialog, ToastContext } from '@majoo-ui/react';
import moment from 'moment';
import { catchError } from '../../../../utils/helper';
import { useMediaQuery } from '../../../../utils/useMediaQuery';
import { STATUS_TYPE } from './settings/utils';
import * as locationsApi from '../../../../data/locations';
import * as usersApi from '../../../../data/users';
import * as employeeApi from '../../../../data/employee';
import ProfileAccount from './components/profileAccountSection';
import PersonalIdentity from './components/identitySection';
import AccessPermission from './components/permissionSection';
import AddressInfo from './components/addressSection';
import UpdatePassword from './components/passwordSection';
import OwnerAppSection from './components/ownerAppSection';
import LanguageSection from './components/languageSection';
import VerificationModal from './components/verificationModal';
import AuthorizationModal from './components/authorizationModal';
import DeleteAccountSection from './components/deleteAccountSection';
import CoreHOC from '../../../../core/CoreHOC';
import { FavoriteWrapper, TooltipGuidance, BannerText } from '../../../../components/retina';
import DeleteTransactionSection from './components/deleteTransactionSection';
import { AccountInfoContext } from './AccountInfoContext';

const AccountInfo = (parentProps) => {
    const {
        showProgress,
        hideProgress,
        idPermission,
        idUser,
        merchantId,
        getContentTranslation,
        businessProfile,
        filterBranch,
        idCabang,
    } = parentProps;
    const isMobile = useMediaQuery('(max-width: 767px)');
    const { t, i18n } = useTranslation(['Pengaturan/AkunProfil/acc', 'translation']);
    const { addToast } = useContext(ToastContext);
    const [isLoading, setLoading] = useState(true);
    const [isUpload, setUpload] = useState(false);
    const [isLoadingCity, setLoadingCity] = useState(false);
    const [verificationModal, setVerificationModal] = useState({
        open: false,
        type: 'phone1',
        isAuth: false,
    });
    const [openAuthorizationModal, setOpenAuthorizationModal] = useState(false);
    const [isSaveModal, setSaveModal] = useState(false);
    const [options, setOptions] = useState({
        provinceOptions: [],
        cityOptions: [],
        subdistrictOptions: [],
    });
    const [formData, setFormData] = useState({
        tempForm: {
            KTPNumber: '',
            KTPUpload: '',
            KTPFileName: '',
            NPWPNumber: '',
            NPWPUpload: '',
            NPWPFileName: '',
        },
        name: '',
        email: '',
        idValidateCode: '',
        phone1: '',
        isVerifiedEmail: false,
        isVerifiedPhone1: false,
        phone2: '',
        phone3: '',
        idSubmissionKtp: '',
        KTPNumber: '',
        KTPUpload: '',
        KTPFileName: '',
        statusKtp: '',
        remarkKtp: '',
        idSubmissionNpwp: '',
        NPWPNumber: '',
        NPWPUpload: '',
        NPWPFileName: '',
        statusNpwp: '',
        remarkNpwp: '',
        permission: '',
        pin: '',
        address: '',
        country: '',
        province: '',
        city: '',
        oldPassword: '',
        newPassword: '',
        confirmNewPassword: '',
        nik: '',
        outlet: '',
    });
    const [ownerAppData, setOwnerAppData] = useState({
        status: true,
        phoneNumber: '',
    });

    const checkIsOwner = () => {
        if (String(idPermission) === '1' && !merchantId) return true;
        return String(idPermission) === '1' && String(idUser) === String(merchantId);
    };

    const schema = yup.object({
        name: yup.string().required(t('v.name', '*Mohon lengkapi Nama')),
        email: yup
            .string()
            .email(t('v.format_email', '*Format email tidak sesuai'))
            .required(t('v.email_empty', '*Email tidak boleh kosong')),
        phone1: yup.string().required(t('v.phone1', '*Mohon lengkapi Telepon ke-1')),
        ...(checkIsOwner() && {
            KTPNumber: yup.string().required(t('v.ktp', '*Mohon lengkapi Nomor KTP')),
            KTPUpload: yup.string().when('KTPNumber', {
                is: value => value.length !== 0,
                then: yup
                    .string()
                    .required(
                        t(
                            'v.ktp_verif',
                            '*KTP belum diverifikasi, mohon upload KTP untuk dapat kami lakukan verifikasi',
                        ),
                    ),
                otherwise: yup.string().required(''),
            }),
        }),
        NPWPUpload: yup.string().when('NPWPNumber', {
            is: value => value.length !== 0,
            then: yup
                .string()
                .required(
                    t('v.npwp_verif', '*NPWP belum diverifikasi, mohon upload KTP untuk dapat kami lakukan verifikasi'),
                ),
        }),
        address: yup.string().required(t('v.address', '*Mohon lengkapi Alamat')),
        province: yup.string().required(t('v.province', '*Mohon lengkapi Provinsi')),
        city: yup.string().required(t('v.city', '*Mohon lengkapi Kota')),
        subdistrict: yup.string().required(t('v.subdistrict', '*Mohon lengkapi Kecamatan')),
        pin: yup
            .string()
            .required(t('v.pin', '*PIN Wajib diisi'))
            .matches('^[0-9]+$', t('v.pin_digit', '*PIN harus menggunakan 6 digit angka'))
            .length(6, t('v.pin_digit', '*PIN harus menggunakan 6 digit angka')),
        confirmNewPassword: yup.string(),
        newPassword: yup
            .string()
            .matches(
                /^.*(?=.{8,})((?=.*[!@#$%^&*()\-_=+{};:,<.>]){1})(?=.*\d)((?=.*[A-Za-z]){1}).*$/,
                t('v.new_password', '*Kata sandi diisi minimal 8 karakter dan berisi angka, huruf, dan simbol'),
                // /^.*(?=.{8,})((?=.*[!@#$%^&*()\-_=+{};:,<.>]){1})(?=.*\d)((?=.*[a-z]){1})((?=.*[A-Z]){1}).*$/, // Minimal 1 huruf kapital
                // '*Kata sandi diisi minimal 8 karakter dan berisi angka, huruf, huruf kapital, dan simbol'
            )
            .oneOf([yup.ref('confirmNewPassword'), null], t('v.new_password1', '*Kata sandi tidak sama'))
            .nullable(),
        language: yup.string(),
    });

    const methods = useForm({
        resolver: yupResolver(schema),
        defaultValues: { ...formData },
    });

    const getChangedFields = currentData => {
        const changed = [];

        if (currentData.email !== formData.email) {
            changed.push('email');
        }

        if (currentData.phone1 !== formData.phone1) {
            changed.push('phone1');
        }

        return changed;
    };

    const provinceId = methods.watch('province', '');
    const cityId = methods.watch('city', '');

    const fetchProvinces = async () => {
        const provinces = [];
        const initProvince = [{ value: '', name: t('translation:select.select') }];
        const response = await locationsApi.getProvince({ id_negara: '107' });
        response.data.map(item =>
            provinces.push({
                value: item.id_provinsi,
                name: getContentTranslation(item.province_name),
            }),
        );
        setOptions(current => ({ ...current, provinceOptions: [...initProvince, ...provinces] }));
    };

    const fetchCities = async idProvinsi => {
        setLoadingCity(true);
        try {
            const cities = await locationsApi.getCity({ id_provinsi: idProvinsi });
            const listCities = [];
            // const initCity = [{ value: '', name: t('translation:select.select') }];
            cities.data.map(item =>
                listCities.push({
                    value: item.id_kota,
                    name: item.kota_name,
                }),
            );
            setOptions(current => ({ ...current, cityOptions: [...listCities] }));
        } catch (e) {
            addToast({
                title: t('translation:toast.error'),
                description: t('translation:error.failedGetDataCustom', { data: t('address.city').toLowerCase() }),
                variant: 'failed',
                position: 'top-right',
            });
        } finally {
            setLoadingCity(false);
        }
    };

    const fetchSubdistricts = async () => {
        const params = {
            limit: 1000,
        };

        if (!cityId) return;

        const response = await usersApi.getDataKecamatan(params, {
            id_kota: cityId,
        });

        if (response.status) {
            setOptions(current => ({
                ...current,
                subdistrictOptions: response.data.map(data => ({
                    value: data.id_kecamatan,
                    name: data.kecamatan_name,
                })),
            }));
        }
    };

    const fetchDataKtpNpwp = async () => {
        const payload = {
            type: 'ktp,npwp',
        };
        let listData = { ...formData };
        const { data } = await usersApi.getListSubmissionProfile(payload);
        if (data.length > 0) {
            // FIND KTP
            const findKtp = data.find(val => val.type === 'ktp');
            let dataKtp = {
                number: '',
                name: '',
                image: '',
                status: '',
            };
            let idSubmissionKtp = '';
            // FIND NPWP
            const findNpwp = data.find(val => val.type === 'npwp');
            let dataNpwp = {
                number: '',
                name: '',
                image: '',
                status: '',
            };
            let idSubmissionNpwp = '';
            // SET DATA KTP
            if (findKtp && findKtp.id) {
                idSubmissionKtp = findKtp.id;
                const result = await usersApi.getDetailSubmissionProfile(findKtp.id);
                const { status_id: status, account_image: image, account_no: number, remark } = result.data;
                const name = image.split('/').pop();
                dataKtp = {
                    number,
                    name,
                    image,
                    status: +status,
                    remark,
                };
            }
            // SET DATA NPWP
            if (findNpwp && findNpwp.id) {
                idSubmissionNpwp = findNpwp.id;
                const result = await usersApi.getDetailSubmissionProfile(findNpwp.id);
                const { status_id: status, account_image: image, account_no: number, remark } = result.data;
                const name = image.split('/').pop();
                dataNpwp = {
                    number,
                    name,
                    image,
                    status: +status,
                    remark,
                };
            }
            // SET STATE FORM DATA
            const KTPNumber = dataKtp.number;
            const KTPUpload = dataKtp.image;
            const KTPFileName = dataKtp.name;
            const NPWPNumber = dataNpwp.number;
            const NPWPUpload = dataNpwp.image;
            const NPWPFileName = dataNpwp.name;
            const tempForm = {
                ...formData.tempForm,
                KTPNumber,
                KTPUpload,
                KTPFileName,
                NPWPNumber,
                NPWPUpload,
                NPWPFileName,
            };
            listData = {
                KTPNumber,
                KTPUpload,
                KTPFileName,
                NPWPNumber,
                NPWPUpload,
                NPWPFileName,
                tempForm,
                statusKtp: dataKtp.status,
                statusNpwp: dataNpwp.status,
                remarkKtp: dataKtp.remark,
                remarkNpwp: dataNpwp.remark,
                idSubmissionNpwp,
                idSubmissionKtp,
            };
            return listData;
        }
        return listData;
    };

    const fetchData = async () => {
        setLoading(true);
        try {
            const response = await usersApi.getAccountInfo();
            await fetchProvinces();
            const dataKtpNpwp = await fetchDataKtpNpwp();
            if (response.status) {
                const { data } = response;
                const dataForm = {
                    name: data.user_name || '',
                    email: data.user_email || '',
                    phone1: data.user_notlp || '',
                    isVerifiedEmail: data.is_verified_email,
                    isVerifiedPhone1: data.is_verified,
                    phone2: data.user_notlp_2 || '',
                    phone3: data.user_notlp_3 || '',
                    permission: data.hak_akses || '',
                    pin: data.user_pin || '',
                    address: data.user_alamat || '',
                    country: data.user_usaha_id_negara ? String(data.user_usaha_id_negara) : '',
                    province: data.user_usaha_id_provinsi ? String(data.user_usaha_id_provinsi) : '',
                    city: data.user_usaha_id_kota ? String(data.user_usaha_id_kota) : '',
                    subdistrict: data.user_usaha_id_kecamatan ? String(data.user_usaha_id_kecamatan) : '',
                    idSubmissionKtp: dataKtpNpwp.idSubmissionKtp,
                    statusKtp: dataKtpNpwp.statusKtp,
                    KTPNumber: dataKtpNpwp.KTPNumber,
                    KTPUpload: dataKtpNpwp.KTPUpload || '',
                    KTPFileName: String(dataKtpNpwp.KTPFileName),
                    remarkKtp: dataKtpNpwp.remarkKtp,
                    idSubmissionNpwp: dataKtpNpwp.idSubmissionNpwp,
                    statusNpwp: dataKtpNpwp.statusNpwp,
                    NPWPNumber: dataKtpNpwp.NPWPNumber,
                    NPWPUpload: dataKtpNpwp.NPWPUpload || '',
                    NPWPFileName: String(dataKtpNpwp.NPWPFileName),
                    remarkNpwp: dataKtpNpwp.remarkNpwp,
                    tempForm: { ...dataKtpNpwp.tempForm },
                    oldPassword: '',
                    newPassword: null,
                    confirmNewPassword: '',
                    nik: '',
                    outlet: '',
                    havePass: data.have_pass || '1',
                    language: data.language || i18n.language,
                };
                setFormData({ ...formData, ...dataForm });
                methods.reset(dataForm);
            }
        } catch (error) {
            addToast({
                title: t('translation:toast.error'),
                description: t('translation:error.failedFetch'),
                variant: 'failed',
                position: 'top-right',
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchRequestVerifCode = async (type, value, isAuth, onSuccess, onError) => {
        showProgress();
        let verificationType = '';
        let payloadValue = value;
        if (isAuth) {
            verificationType = 'authorize_employee_profile';
            payloadValue = getChangedFields(methods.getValues())
                .map(field => methods.getValues(field))
                .join(',');
        } else if (type === 'email') {
            verificationType = 'verify_employee_profile_email';
        } else {
            verificationType = 'verify_employee_profile_phone';
        }

        let sendToType;
        if (type === 'email') {
            sendToType = 'email';
        } else {
            sendToType = 'phone';
        }

        const payload = {
            type: verificationType,
            value: payloadValue,
            send_to: sendToType,
        };
        try {
            await employeeApi.requestVerificationCode(payload);
            if (onSuccess) onSuccess();
            setVerificationModal({
                open: true,
                type,
                isAuth,
            });
            setOpenAuthorizationModal(false);
        } catch (error) {
            if (onError) onError(error);
            addToast({
                title: t('translation:toast.error'),
                description: catchError(error),
                variant: 'failed',
                position: 'top-right',
            });
        } finally {
            hideProgress();
        }
    };

    const fetchVerifyData = async (code, type, isAuth, onError) => {
        setLoading(true);
        try {
            let verificationType;
            if (isAuth) {
                verificationType = 'authorize_employee_profile';
            } else if (type === 'email') {
                verificationType = 'verify_employee_profile_email';
            } else {
                verificationType = 'verify_employee_profile_phone';
            }

            await employeeApi.verifyCode({
                type: verificationType,
                code,
            });

            if (isAuth) {
                setSaveModal(true);
            } else if (type === 'email') {
                setFormData({ ...formData, isVerifiedEmail: true });
                methods.setValue('isVerifiedEmail', true);
                addToast({
                    title: t('translation:toast.success'),
                    description: t('success.email', 'Email berhasil diverifikasi'),
                    variant: 'success',
                    position: 'top-right',
                });
            } else if (type === 'phone1') {
                setFormData({ ...formData, isVerifiedPhone1: true });
                methods.setValue('isVerifiedPhone1', true);
                addToast({
                    title: t('translation:toast.success'),
                    description: t('success.phone'),
                    variant: 'success',
                    position: 'top-right',
                });
            }

            setVerificationModal({ open: false });
        } catch (error) {
            if (onError) onError(error);
        } finally {
            methods.setValue('verificationCode', '');
            setLoading(false);
        }
    };

    const saveDataKtpNpwp = async (method, payload) => {
        if (method === 'create') {
            await usersApi.createSubmissionProfile(payload);
        } else {
            await usersApi.updateSubmissionProfile(payload);
        }
    };

    const handleSaveDataKtpNpwp = data => {
        // CHECK KTP
        if (data.KTPNumber !== formData.tempForm.KTPNumber || data.KTPUpload !== formData.tempForm.KTPUpload) {
            const payload = {
                type: 'ktp',
                account_no: data.KTPNumber,
                account_image: data.KTPUpload,
            };
            if (
                formData.idSubmissionKtp !== '' &&
                formData.statusKtp !== STATUS_TYPE.ON_PROCESS &&
                formData.statusKtp !== STATUS_TYPE.APPROVE
            ) {
                const payloadWithId = { ...payload, id: formData.idSubmissionKtp };
                saveDataKtpNpwp('update', payloadWithId, 'ktp');
            } else if (formData.tempForm.KTPNumber === '') {
                saveDataKtpNpwp('create', payload, 'ktp');
            }
        }
        // CHECK NPWP
        if (data.NPWPNumber !== formData.tempForm.NPWPNumber || data.NPWPUpload !== formData.tempForm.NPWPUpload) {
            const payload = {
                type: 'npwp',
                account_no: data.NPWPNumber,
                account_image: data.NPWPUpload,
            };
            if (
                formData.idSubmissionNpwp !== '' &&
                formData.statusNpwp !== STATUS_TYPE.ON_PROCESS &&
                formData.statusNpwp !== STATUS_TYPE.APPROVE
            ) {
                const payloadWithId = { ...payload, id: formData.idSubmissionNpwp };
                saveDataKtpNpwp('update', payloadWithId, 'npwp');
            } else if (formData.tempForm.NPWPNumber === '') {
                saveDataKtpNpwp('create', payload, 'npwp');
            }
        }
    };

    const fetchUpdateAccount = async data => {
        const payload = {
            nama: data.name,
            PIN: data.pin,
            email: data.email,
            no_tlp: data.phone1,
            no_tlp_2: data.phone2.length > 7 ? data.phone2 : '',
            no_tlp_3: data.phone3.length > 7 ? data.phone3 : '',
            alamat: data.address,
            id_kota: data.city,
            id_provinsi: data.province,
            id_kecamatan: data.subdistrict,
            password_old: data.oldPassword,
            password_new: data.newPassword,
            password_new_repeat: data.confirmNewPassword,
            language: data.language,
        };
        setLoading(true);
        try {
            setSaveModal(false);
            handleSaveDataKtpNpwp(data);
            const response = await usersApi.updateInfoAkun(payload, '0_0_1');
            if (!response.status) throw new Error(response.msg);
            moment.locale(data.language);
            i18n.changeLanguage(data.language);
            setTimeout(
                () =>
                    addToast({
                        title: t('translation:toast.success'),
                        description: t('success.infoacc'),
                        variant: 'success',
                        position: 'top-right',
                    }),
                100,
            );
            fetchData();
        } catch (error) {
            if (error.message.includes('old password')) {
                addToast({
                    title: t('translation:toast.error'),
                    description: t('v.oldpw'),
                    variant: 'failed',
                    position: 'top-right',
                });
            } else {
                addToast({
                    title: t('translation:toast.error'),
                    description: t('v.infoacc'),
                    variant: 'failed',
                    position: 'top-right',
                });
            }
        } finally {
            setLoading(false);
        }
    };

    const fetchOwnerAppData = async () => {
        try {
            const { status, data } = await usersApi.getOwnerAppData();
            if (status) {
                let formattedPhone;
                const phonePrefix = data.phone_number.substring(0, 3);

                if (phonePrefix === '+62') {
                    formattedPhone = `0${data.phone_number.substring(3, data.phone_number.length)}`;
                } else {
                    formattedPhone = data.phone_number;
                }

                setOwnerAppData({
                    ...ownerAppData,
                    status: data.status,
                    phoneNumber: formattedPhone,
                });
            }
        } catch (error) {
            addToast({
                title: t('translation:toast.error'),
                description: catchError(error),
                variant: 'failed',
                position: 'top-right',
            });
        }
    };

    const handleSaveModal = () => {
        if (!methods.getValues('isVerifiedEmail') || !methods.getValues('isVerifiedPhone1')) {
            return;
        }

        const currentFormData = methods.getValues();
        const changedFields = getChangedFields(currentFormData);

        if (changedFields.length > 0) {
            setOpenAuthorizationModal(true);
        } else {
            setSaveModal(!isSaveModal);
        }
    };

    const onSubmit = data => {
        fetchUpdateAccount(data);
    };

    useEffect(() => {
        fetchData();
        fetchOwnerAppData();
    }, []);

    useEffect(() => {
        if (isLoading) {
            showProgress();
        } else {
            hideProgress();
        }
    }, [isLoading]);

    useEffect(() => {
        fetchProvinces();
    }, [i18n.language]);

    useEffect(async () => {
        await fetchCities(provinceId);
    }, [provinceId]);

    useEffect(() => {
        fetchSubdistricts();
    }, [cityId]);

    const contextValue = {
        ...parentProps,
        isMobile,
        t,
        i18n,
        addToast,
        isLoading,
        setLoading,
        isUpload,
        setUpload,
        isLoadingCity,
        setLoadingCity,
        verificationModal,
        setVerificationModal,
        openAuthorizationModal,
        setOpenAuthorizationModal,
        isSaveModal,
        setSaveModal,
        options,
        setOptions,
        formData,
        setFormData,
        ownerAppData,
        setOwnerAppData,
        methods,
        provinceId,
        cityId,
        fetchProvinces,
        fetchCities,
        fetchSubdistricts,
        fetchDataKtpNpwp,
        fetchData,
        fetchRequestVerifCode,
        fetchVerifyData,
        saveDataKtpNpwp,
        handleSaveDataKtpNpwp,
        fetchUpdateAccount,
        fetchOwnerAppData,
        handleSaveModal,
        onSubmit,
        checkIsOwner,
        getChangedFields,
    };

    return (
        <Paper responsive css={{ padding: 'unset', position: 'relative' }}>
            <Box css={{ padding: 0, '@md': { padding: '20px 20px 0 20px' } }}>
                <FavoriteWrapper>
                    <Heading heading="pageTitle" css={{ '@sm': { margin: '20px 0 !important' } }}>
                        {t('title', 'Informasi Akun')}
                    </Heading>
                    <TooltipGuidance />
                </FavoriteWrapper>
                <BannerText css={{ my: 0 }} />
                <Separator responsive css={{ margin: '20px 0' }} />
                <FormProvider {...methods}>
                    <AccountInfoContext.Provider value={contextValue}>

                    <form onSubmit={methods.handleSubmit(handleSaveModal)} id="account-info-form">
                        <ProfileAccount
                            requestVerificationCode={(type, value) => fetchRequestVerifCode(type, value)}
                            formData={formData}
                            isMobile={isMobile}
                            t={t}
                        />
                        <Separator css={{ display: 'block', margin: '30px 0' }} />
                        {checkIsOwner() && (
                            <React.Fragment>
                                <PersonalIdentity isUploading={value => setUpload(value)} isMobile={isMobile} t={t} />
                                <Separator css={{ display: 'block', margin: '30px 0' }} />
                            </React.Fragment>
                        )}
                        <AccessPermission isMobile={isMobile} t={t} />
                        <Separator css={{ display: 'block', margin: '30px 0' }} />
                        <AddressInfo
                            key={i18n.language}
                            t={t}
                            provinceOptions={options.provinceOptions}
                            cityOptions={options.cityOptions}
                            subdistrictOptions={options.subdistrictOptions}
                            fetchCities={data => fetchCities(data)}
                            isLoadingCity={isLoadingCity}
                            isMobile={isMobile}
                        />
                        <Separator css={{ display: 'block', margin: '30px 0' }} />
                        <UpdatePassword isMobile={isMobile} t={t} />
                        {checkIsOwner() && (
                            <React.Fragment>
                                <Separator css={{ display: 'block', margin: '30px 0' }} />
                                <OwnerAppSection data={ownerAppData} t={t} />
                            </React.Fragment>
                        )}
                        <Separator css={{ display: 'block', margin: '30px 0' }} />
                        <LanguageSection isMobile={isMobile} />
                        <Separator css={{ display: 'block', margin: '30px 0' }} />
                        {checkIsOwner() && (
                            <React.Fragment>
                                <DeleteAccountSection
                                    isMobile={isMobile}
                                    addToast={addToast}
                                    showProgress={showProgress}
                                    hideProgress={hideProgress}
                                    formData={formData}
                                    businessProfile={businessProfile}
                                    outletId={filterBranch || idCabang}
                                />
                                <Separator css={{ display: 'block', my: '30px' }} />
                            </React.Fragment>
                        )}
                        <DeleteTransactionSection
                            isMobile={isMobile}
                            addToast={addToast}
                            showProgress={showProgress}
                            hideProgress={hideProgress}
                            formData={formData}
                            t={t}
                        />
                        <Separator css={{ display: 'block', mt: '30px' }} />
                    </form>
                    {openAuthorizationModal && (
                        <AuthorizationModal
                            isOpen={openAuthorizationModal}
                            onOpenChange={setOpenAuthorizationModal}
                            phoneNumber={formData.phone1}
                            email={formData.email}
                            requestVerification={(contactValue, type) => fetchRequestVerifCode(type, contactValue, true)}
                            isMobile={isMobile}
                            t={t}
                        />
                    )}
                    {verificationModal.open && (
                        <VerificationModal
                            isMobile={isMobile}
                            isOpen={verificationModal.open}
                            onOpenChange={open => setVerificationModal(current => ({ ...current, open }))}
                            verificationType={verificationModal.type}
                            requestVerificationCode={async (type, value, onSuccess, onError) => {
                                setLoading(true);
                                await fetchRequestVerifCode(type, value, verificationModal.isAuth, onSuccess, onError);
                                setLoading(false);
                            }}
                            onSubmit={(code, type, onError) =>
                                fetchVerifyData(code, type, verificationModal.isAuth, onError)
                            }
                            t={t}
                            isAuth={verificationModal.isAuth}
                            contactValue={methods.getValues(verificationModal.type)}
                        />
                    )}

                    <AlertDialog
                        isMobile={isMobile}
                        open={isSaveModal}
                        title={t('save')}
                        description={<Box css={{ lineHeight: '25px' }}>{t('desc')}</Box>}
                        labelConfirm={t('translation:label.continue')}
                        labelCancel={t('translation:label.cancel')}
                        onCancel={() => setSaveModal(false)}
                        onConfirm={methods.handleSubmit(onSubmit)}
                        css={{ width: !isMobile && '422px' }}
                    />
                    </AccountInfoContext.Provider>

                </FormProvider>
            </Box>
            <Flex
                justify="end"
                css={{
                    padding: '20px 1px',
                    backgroundColor: '$white',
                    position: 'sticky',
                    bottom: 0,
                    '@md': { padding: 20 },
                }}
            >
                <Button type="submit" form="account-info-form" disabled={isUpload} block={isMobile}>
                    {t('translation:label.save')}
                </Button>
            </Flex>
        </Paper>
    );
};

AccountInfo.propTypes = {
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
    idPermission: PropTypes.string.isRequired,
    idUser: PropTypes.string.isRequired,
    merchantId: PropTypes.string.isRequired,
    isMultiLanguage: PropTypes.number,
    getContentTranslation: PropTypes.func.isRequired,
    businessProfile: PropTypes.shape({}),
};

AccountInfo.defaultProps = {
    showProgress: () => {},
    hideProgress: () => {},
    isMultiLanguage: 0,
    businessProfile: {
        ownerEmail: '',
        ownerPhone: '',
    },
};

const mapStateToProps = state => ({
    account: state.accountInfo.accountInfoResult,
    isMultiLanguage: state.users.strap.isMultiLanguage,
    listOutlet: state.branch.list,
    businessProfile: {
        ownerPhone: state.user.profile.user_usaha_notlp,
        ownerEmail: state.user.profile.user_usaha_email,
    },
});

export default CoreHOC(connect(mapStateToProps)(AccountInfo));
