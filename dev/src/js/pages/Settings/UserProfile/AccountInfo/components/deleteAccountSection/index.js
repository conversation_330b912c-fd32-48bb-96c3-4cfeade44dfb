import React, { useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Box, Flex, Heading, Button, Text, AlertDialog } from '@majoo-ui/react';
import { useTranslation, Trans } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { styled } from '~/stitches.config';
import { catchError } from '~/utils/helper';
import { get } from 'lodash';
import moment from 'moment';
import DeleteAccountDialog from './deleteAccountDialog';
import { postDeleteAccount, checkDeleteAccount } from '../../../../../../data/users';
import { usersActions } from '../../../../../../data/users/users.reducer';
import * as employeeApi from '../../../../../../data/employee';
import AuthorizationModal from '../authorizationModal';
import VerificationModal from '../verificationModal';
import DeleteAccountInfoModal from './deleteAccountInfoModal';

const ClickableText = styled(Text, {
    color: '$textGreen',
    fontWeight: 600,
    '&:hover': {
        cursor: 'pointer',
    },
});

const DeleteAccountSection = ({
    isMobile,
    showProgress,
    hideProgress,
    addToast,
    formData,
    updateAccountDetail,
    deleteDetailInfo,
    businessProfile,
    outletId,
}) => {
    const { t } = useTranslation(['Pengaturan/AkunProfil/acc', 'translation']);
    const [isOpenDialog, setIsOpenDialog] = useState(false);
    const [isDeleteAlert, setIsDeleteAlert] = useState(false);
    const createdAt = useMemo(() => get(deleteDetailInfo, 'created_at'), [deleteDetailInfo]);

    // Authorization and Verification Modal states
    const [openAuthorizationModal, setOpenAuthorizationModal] = useState(false);
    const [openDeleteAccountInfoModal, setOpenDeleteAccountInfoModal] = useState(false);
    const [verificationModal, setVerificationModal] = useState({
        open: false,
        type: 'phone1',
        isAuth: false,
        contactValue: '',
    });
    const [userData, setUserData] = useState({
        email: businessProfile.ownerEmail,
        phone: businessProfile.ownerPhone,
    });

    // Request verification code for authorization
    const fetchRequestVerifCode = async (contactValue, verificationType, onSuccess, onError) => {
        showProgress();
        const verificationPayload = {
            type: 'authorize_delete_account',
            value: contactValue,
            send_to: verificationType,
        };

        try {
            await employeeApi.requestVerificationCode(verificationPayload);
            if (onSuccess) onSuccess();
            setVerificationModal({
                open: true,
                type: verificationType,
                contactValue,
            });
            setOpenAuthorizationModal(false);
        } catch (error) {
            if (onError) onError(error);
            addToast({
                title: t('translation:toast.error'),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    // Verify the code entered by user
    const fetchVerifyData = async (code, _verificationType, onError) => {
        showProgress();
        try {
            await employeeApi.verifyCode({
                type: 'authorize_delete_account',
                code,
            });

            setVerificationModal({ open: false });
            setIsOpenDialog(true);
        } catch (error) {
            if (onError) onError(error);
            addToast({
                title: t('translation:toast.error'),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const handleDeleteAccount = async () => {
        try {
            showProgress();
            await postDeleteAccount();
            const { data } = await checkDeleteAccount();
            if (data.length > 0) {
                updateAccountDetail({ ...deleteDetailInfo, ...data[0] });
            }
            addToast({
                title: t('translation:toast.success'),
                description: t('delete.toastSuccess', 'Pengajuan hapus akun berhasil dilakukan'),
                variant: 'success',
            });
            setIsOpenDialog(false);
            setIsDeleteAlert(false);
        } catch (error) {
            addToast({
                title: t('translation:toast.failed'),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    return (
        <React.Fragment>
            <Flex direction={!isMobile ? 'row' : 'column'} justify="between" gap={3}>
                <Flex direction="column" gap={3}>
                    <Heading as="h3" heading="sectionTitle" css={{ '@sm': { mb: '$spacing-05 !important' } }}>
                        {t('delete.title', 'Pengajuan Hapus Akun')}
                    </Heading>
                    <Text css={{ colort: '$textDisable', width: '$full', '@lg': { width: 250 } }}>
                        {t('delete.info', 'Proses hapus akun akan memutus semua integrasi pada platform majoo')}
                    </Text>
                </Flex>
                <Flex direction={!isMobile ? 'row' : 'column'} css={{ width: !isMobile ? '70%' : '100%' }} gap={4}>
                    <Button
                        type="button"
                        buttonType="negative"
                        onClick={async () => {
                            if (!userData.email || !userData.phone) {
                                setUserData(current => ({
                                    email: current.email || formData.email || '',
                                    phone: current.phone || formData.phone1 || '',
                                }));
                            }
                            setOpenAuthorizationModal(true);
                        }}
                        disabled={createdAt}
                    >
                        {t('delete.button')}
                    </Button>
                    {createdAt && (
                        <Box>
                            <Text color="primary">
                                {t('delete.createdAt', { date: moment(createdAt).format('DD MMMM YYYY') })}
                            </Text>
                            <ClickableText
                                onClick={async () => {
                                    setOpenDeleteAccountInfoModal(true);
                                }}
                            >
                                {t('delete.viewInformationService')}
                            </ClickableText>
                        </Box>
                    )}
                </Flex>
            </Flex>
            {/* Authorization Modal */}
            {openAuthorizationModal && (
                <AuthorizationModal
                    title={t('delete.authorization', 'Otorisasi Hapus Akun Merchant')}
                    isOpen={openAuthorizationModal}
                    onOpenChange={setOpenAuthorizationModal}
                    phoneNumber={userData.phone}
                    email={userData.email}
                    requestVerification={(contactValue, verificationType) => fetchRequestVerifCode(contactValue, verificationType)}
                    isMobile={isMobile}
                    t={t}
                    toOwner
                    outletId={outletId}
                />
            )}

            {/* Verification Modal */}
            {verificationModal.open && (
                <VerificationModal
                    isMobile={isMobile}
                    isOpen={verificationModal.open}
                    onOpenChange={isOpen => setVerificationModal(current => ({ ...current, open: isOpen }))}
                    verificationType={verificationModal.type}
                    contactValue={verificationModal.contactValue || (verificationModal.type === 'email' ? userData.email : userData.phone)}
                    requestVerificationCode={async (verificationType, contactValue, onSuccess, onError) => {
                        await fetchRequestVerifCode(contactValue, verificationType, onSuccess, onError);
                    }}
                    onSubmit={(code, verificationType, onError) => fetchVerifyData(code, verificationType, onError)}
                    t={t}
                />
            )}
            {isOpenDialog && (
                <DeleteAccountDialog
                    isOpen={isOpenDialog}
                    onOpenChange={setIsOpenDialog}
                    t={t}
                    isMobile={isMobile}
                    handleSubmit={() => setIsDeleteAlert(true)}
                />
            )}
            {openDeleteAccountInfoModal && (
                <DeleteAccountInfoModal
                    isOpen={openDeleteAccountInfoModal}
                    onOpenChange={setOpenDeleteAccountInfoModal}
                    isMobile={isMobile}
                    t={t}
                />
            )}
            <AlertDialog
                dialogType="negative"
                isMobile={isMobile}
                open={isDeleteAlert}
                title={t('delete.alert.title', 'Ajukan Penghapusan Akun')}
                description={<Trans t={t} i18nKey="delete.alert.description" />}
                labelConfirm={t('translation:label.continue')}
                labelCancel={t('translation:label.cancel')}
                onCancel={() => setIsDeleteAlert(false)}
                onConfirm={handleDeleteAccount}
            />
        </React.Fragment>
    );
};

DeleteAccountSection.propTypes = {
    isMobile: PropTypes.bool,
    addToast: PropTypes.func,
    outletId: PropTypes.string,
};

DeleteAccountSection.defaultProps = {
    isMobile: false,
    addToast: () => {},
    outletId: ''
};

export default connect(
    store => ({
        deleteDetailInfo: store.users.deleteDetailInfo,
    }),
    dispatch => ({
        ...bindActionCreators(
            {
                updateAccountDetail: usersActions.updateAccountDetail,
            },
            dispatch,
        ),
    }),
)(DeleteAccountSection);
