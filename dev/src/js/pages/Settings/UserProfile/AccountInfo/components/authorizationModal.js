import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { ModalDialog, ModalDialogTitle, ModalDialogContent, Button, Text, Box, Flex } from '@majoo-ui/react';
import { EnvelopeFilled, WhatsappField } from '@majoo-ui/icons';
import { colors } from '~/stitches.config';
import { getOwnerData } from '~/data/users';

const AuthorizationModal = ({
    title,
    isOpen,
    onOpenChange,
    phoneNumber,
    email,
    requestVerification,
    isMobile,
    t,
    toOwner,
    outletId,
}) => {
    const [ownerData, setOwnerData] = useState({
        phoneNumber,
        email,
    });

    const handleButtonClick = type => {
        requestVerification(type === 'phone' ? ownerData.phoneNumber : ownerData.email, type);
    };

    const handleFetchOwnerData = async () => {
        try {
            const payload = { outlet_id: outletId };
            const res = await getOwnerData(payload);
            if (!res.status) throw new Error(res.message);
            const { phone_number: phoneNumber, email } = res.data;
            setOwnerData({ phoneNumber, email });
        } catch (error) {
            console.error({ error });
        }
    };

    useEffect(() => {
        if (toOwner && isOpen) {
            handleFetchOwnerData(1);
        }
    }, [toOwner, isOpen]);

    return (
        <ModalDialog modal open={isOpen} onOpenChange={onOpenChange} isMobile={isMobile}>
            <ModalDialogTitle css={{ position: 'relative' }}>
                {title || t('authorization.title', 'Otorisasi Akun Profile')}
            </ModalDialogTitle>

            <ModalDialogContent>
                <Box css={{ marginBottom: '$spacing-05' }}>
                    <Text
                        color="primary"
                        css={{
                            fontSize: '$body-regular',
                            lineHeight: '1.5',
                            textAlign: 'left',
                        }}
                    >
                        {t(
                            'authorization.description',
                            'Pilih salah satu metode di bawah ini untuk mendapatkan kode verifikasi',
                        )}
                    </Text>
                </Box>

                <Flex direction="column" gap="5">
                    <Button
                        buttonType="secondary"
                        leftIcon={<WhatsappField color={colors.iconGreen} />}
                        onClick={() => handleButtonClick('phone')}
                    >
                        {t('authorization.whatsapp', 'WhatsApp ke {{phone}}', { phone: ownerData.phoneNumber })}
                    </Button>

                    <Button
                        buttonType="secondary"
                        leftIcon={<EnvelopeFilled color={colors.iconGreen} />}
                        onClick={() => handleButtonClick('email')}
                    >
                        {t('authorization.email', 'Email ke {{email}}', { email: ownerData.email })}
                    </Button>
                </Flex>
            </ModalDialogContent>
        </ModalDialog>
    );
};

AuthorizationModal.propTypes = {
    title: PropTypes.string,
    isOpen: PropTypes.bool.isRequired,
    onOpenChange: PropTypes.func.isRequired,
    phoneNumber: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    requestVerification: PropTypes.func.isRequired,
    isMobile: PropTypes.bool,
    t: PropTypes.func.isRequired,
    toOwner: PropTypes.bool,
    outletId: PropTypes.string,
};

AuthorizationModal.defaultProps = {
    title: undefined,
    isMobile: false,
    toOwner: false,
    outletId: '',
};

export default AuthorizationModal;
