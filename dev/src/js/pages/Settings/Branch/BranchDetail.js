import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { PageDialog, ToastContext, Box, Flex, Button, IconButton, Tooltip } from '@majoo-ui/react';
import { TrashOutline, EllipsisHorizontalOutline } from '@majoo-ui/icons';
import { useForm } from 'react-hook-form';
import { get } from 'lodash';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Trans, useTranslation } from 'react-i18next';
import moment from 'moment';
import CoreHOC from '../../../core/CoreHOC';

import { catchError } from '../../../utils/helper';
import * as outletApi from '../../../data/outlets';
import * as locationActionCreators from '../../../data/locations/actions';

import { filterCabang, fetchCabang } from '../../../actions/branchActions';
import * as employeeApi from '../../../data/employee';
import AuthorizationModal from '../UserProfile/AccountInfo/components/authorizationModal';
import VerificationModal from '../UserProfile/AccountInfo/components/verificationModal';

import { OUTLET_TYPE, OUTLET_STATUS, SOCIAL_OPTIONS } from './Branch/retina/enum';
import { FormOutletInformation } from './Branch/retina/FormOutletInformation';
import { ConfirmModal, OptionModal, PinModal, WarningModal } from './Branch/retina/Modal';
import { useMediaQuery } from '../../../utils/useMediaQuery';
import { colors } from '../../../stitches.config';
import * as payrollApi from '../../../data/payment';
import { updateOnboardingTask } from '../../../services/session';
import { CoachMarkOutlet, CoachMarkModal, ACTIONS, outletSteps } from './components/CoachMark';

const defaultSchedule = ['Senin', 'Selasa', 'Rabu', 'Kamis', "Jum'at", 'Sabtu', 'Minggu'].map(day => ({
    date: day,
    status: false,
    isOpenAllDay: false,
    time: [{ start: '00:00', end: '23:59', isOverlapping: false }],
}));

const schema = t =>
    yup.object().shape({
        cabang_name: yup.string().required(t('name.error')),
        id_provinsi: yup.string().required(t('address.errors.province')).nullable(),
        id_kota: yup.string().required(t('address.errors.city')).nullable(),
        cabang_address: yup.string().required(t('address.errors.details')).nullable(),
        cabang_notlp: yup.string().required(t('phone.errors.required')).min(8, t('phone.errors.min')).nullable(),
        cabang_email: yup.string().email(t('modal.employee.email.format')),
        cabang_open_hour: yup.object().shape({
            all_day: yup.bool(),
            jadwal: yup.array().of(
                yup.object().shape({
                    time: yup.array().of(
                        yup.object().shape({
                            isOverlapping: yup.bool().test('arr', t('scheduleError'), x => x === false),
                        }),
                    ),
                }),
            ),
        }),
    });

const BranchDetail = props => {
    const {
        hideProgress,
        params,
        router,
        showProgress,
        idPermission,
        idUser,
        merchantId,
        profile,
        filterBranch,
        idCabang,
    } = props;
    const { t } = useTranslation(['Pengaturan/outletDetail', 'translation']);
    const { addToast } = React.useContext(ToastContext);
    const [outletDetail, setOutletDetail] = useState({});
    const [openConfirm, setOpenConfirm] = useState(false);
    const [openCancel, setOpenCancel] = useState(false);
    const [openDelete, setOpenDelete] = useState(false);
    const [openWarning, setOpenWarning] = useState(false);
    const [openOption, setOpenOption] = useState(false);
    const [openPin, setOpenPin] = useState(false);
    const [hasActivePayroll, setHasActivePayroll] = useState(true);
    const [showCoachMark, setShowCoachMark] = useState(true);
    const [showModalCoachMark, setShowModalCoachMark] = useState(false);
    const isMobile = useMediaQuery('(max-width: 767px)');
    const mapsQuery = new URLSearchParams(window.location.search).get('maps');
    const usingSuppliesMaps = mapsQuery === 'supplies';
    const TooltipDelete = hasActivePayroll ? Tooltip : Box;

    // Authorization and Verification Modal states
    const [openAuthorizationModal, setOpenAuthorizationModal] = useState(false);
    const [verificationModal, setVerificationModal] = useState({
        open: false,
        type: 'phone',
        isAuth: false,
        contactValue: ''
    });
    const [userData, setUserData] = useState({
        email: profile.user_usaha_email || '',
        phone: profile.user_usaha_notlp || '',
    });

    const hookForm = useForm({
        resolver: yupResolver(schema(t)),
        defaultValues: {
            id_outlet: '',
            status: OUTLET_STATUS.OPEN,
            cabang_logo_path: '',
            cabang_logo_path_bill: '',
            cabang_name: '',
            cabang_email: '',
            cabang_notlp: '',
            cabang_no_whatsapp: '',
            cabang_address: '',
            cabang_postal_code: '',
            cabang_address_note: '',
            cabang_longitude: '',
            cabang_latitude: '',
            cabang_media_sosial: [],
            cabang_jenis_outlet: OUTLET_TYPE.PENJUALAN,
            cabang_open_hour: {
                all_day: false,
                jadwal: [],
            },
            id_supervisor: '',
            id_provinsi: '',
            id_kota: '',
            id_negara: '107',
        },
    });

    const checkIsOwner = () => {
        if (String(idPermission) === '1' && !merchantId) return true;
        return String(idPermission) === '1' && String(idUser) === String(merchantId);
    };

    const { getValues, reset, handleSubmit } = hookForm;

    const [open, setOpen] = useState(true);
    const {
        location: { query },
    } = router;
    const redirectPath = query && query.redirect ? query.redirect : '/pengaturan-bisnis/cabang';

    const fetchOutletDetail = async (param, resetValue = true) => {
        showProgress();
        try {
            const res = await outletApi.getCabangDetail({ id_outlet: param });
            if (!res.status) throw new Error(res.msg);
            const { data } = res;
            if (!resetValue) {
                setOutletDetail(data);
                return;
            }

            const isCabangOpenHourObject = data.cabang_open_hour instanceof Object;

            reset({
                ...getValues(),
                id_outlet: data.id_cabang,
                cabang_name: data.cabang_name,
                cabang_jenis_outlet: data.cabang_jenis_outlet,
                status: data.active_status,
                cabang_address: data.cabang_address,
                cabang_address_note: data.cabang_address_note,
                id_supervisor: get(data, 'supervisor.id_supervisor', ''),
                cabang_logo_path: data.cabang_logo_path,
                cabang_logo_path_bill: data.cabang_logo_path_bill,
                cabang_email: data.cabang_email,
                cabang_open_hour: {
                    all_day: isCabangOpenHourObject ? data.cabang_open_hour.all_day || false : false,
                    jadwal:
                        isCabangOpenHourObject &&
                        Array.isArray(data.cabang_open_hour.jadwal) &&
                        data.cabang_open_hour.jadwal.length > 0
                            ? data.cabang_open_hour.jadwal
                            : defaultSchedule,
                    temporary_close:
                        isCabangOpenHourObject &&
                        Array.isArray(data.cabang_open_hour.temporary_close) &&
                        data.cabang_open_hour.temporary_close.length > 0
                            ? data.cabang_open_hour.temporary_close
                            : [],
                },
                cabang_latitude: data.cabang_latitude,
                cabang_longitude: data.cabang_longitude,
                id_provinsi: data.id_provinsi,
                id_kota: data.id_kota,
                cabang_notlp: data.cabang_notlp,
                cabang_no_whatsapp: data.cabang_no_whatsapp,
                cabang_postal_code: data.cabang_postal_code,
                cabang_media_sosial:
                    get(data, 'cabang_media_sosial.length', 0) > 0
                        ? data.cabang_media_sosial.map(d => ({
                              id_media_sosial: d.M_Media_Sosial_id_media_sosial,
                              media_sosial_link: d.link,
                          }))
                        : [{ id_media_sosial: SOCIAL_OPTIONS[1].value, media_sosial_link: '' }],
            });
            setOutletDetail(data);
        } catch (e) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const updateOutlet = async param => {
        showProgress();
        try {
            const payload = {
                ...param,
                cabang_media_sosial: param.cabang_media_sosial.filter(x => x.id_media_sosial && x.media_sosial_link),
            };
            const res = await outletApi.cabangUpdate(payload);
            if (!res.status) throw new Error(res.msg);
            await updateOnboardingTask({
                outletPage: 1,
            });
            addToast({
                title: t('toast.success', { ns: 'translation' }),
                description: (
                    <Trans t={t} i18nKey="toast.success">
                        {{ name: param.cabang_name }}
                    </Trans>
                ),
                variant: 'success',
            });
            if (redirectPath) {
                setTimeout(() => {
                    router.replace(redirectPath);
                }, 600);

                return;
            }

            setTimeout(() => {
                setOpen(false);
                setOpenConfirm(false);
            }, 200);
        } catch (e) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
            setTimeout(() => {
                setOpen(false);
                setOpenConfirm(false);
            }, 200);
        } finally {
            hideProgress();
        }
    };

    const deleteOutlet = async param => {
        showProgress();
        const payload = {
            id_outlet: params.id,
            user_pin: param,
        };
        try {
            const res = await outletApi.cabangDelete(payload);
            if (!res.status) throw new Error(res.msg);
            addToast({
                title: t('toast.success', { ns: 'translation' }),
                description: (
                    <Trans t={t} i18nKey="toast.delete">
                        {{ name: getValues('cabang_name') }}
                    </Trans>
                ),
                variant: 'success',
            });
            setOpenDelete(false);
            router.replace(redirectPath);
        } catch (e) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const handleCloseModal = val => {
        if (!val) {
            setOpenCancel(true);
        }
    };

    const handleUpdateOutlet = () => {
        handleSubmit(p => {
            updateOutlet(p);
        })();
    };

    const handleNextButton = () => {
        handleSubmit(() => {
            setOpenConfirm(true);
        })();
    };

    const handleBackButton = () => {
        setOpenCancel(true);
    };

    const handleDelete = async () => {
        const res = await outletApi.cabangCheckKaryawan({ id_outlet: params.id });
        if (res.jumlah > 0) {
            setOpenDelete(false);
            setOpenWarning(true);
            return;
        }
        setOpenDelete(false);
        if (!userData.email || !userData.phone) {
            setUserData(current => ({
                email: current.email || getValues('cabang_email') || '',
                phone: current.phone || getValues('cabang_notlp') || '',
            }));
        }
        setOpenAuthorizationModal(true);
    };

    const handleCancel = () => {
        setOpenCancel(false);
        setOpen(false);
        router.replace(redirectPath);
    };

    const handleSubmitPin = number => {
        deleteOutlet(number);
    };

    // Request verification code for authorization
    const fetchRequestVerifCode = async (contactValue, verificationType, onSuccess, onError) => {
        showProgress();
        const verificationPayload = {
            type: 'authorize_delete_cabang',
            value: contactValue,
            send_to: verificationType,
        };

        try {
            await employeeApi.requestVerificationCode(verificationPayload);
            if (onSuccess) onSuccess();
            setVerificationModal({
                open: true,
                type: verificationType,
                contactValue,
            });
            setOpenAuthorizationModal(false);
        } catch (error) {
            if (onError) onError(error);
            addToast({
                title: t('translation:toast.error'),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    // Verify the code entered by user
    const fetchVerifyData = async (code, _verificationType, onError) => {
        showProgress();
        try {
            await employeeApi.verifyCode({
                type: 'authorize_delete_cabang',
                code,
            });

            setVerificationModal({ open: false });
            setOpenPin(true);
        } catch (error) {
            if (onError) onError(error);
            addToast({
                title: t('translation:toast.error'),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const fetchPayroll = async id => {
        const statusForChecking = ['on_process', 'scheduled'];
        const payload = {
            payment_start_date: moment().format('YYYY-MM-DD'),
            payment_end_date: moment().add(2, 'months').format('YYYY-MM-DD'),
            page: 1,
            limit: 10,
            outlet_id: id,
        };
        const promises = statusForChecking.map(status => payrollApi.getPaymentTransaction({ ...payload, status }));
        const res = await Promise.all(promises);
        const dataPayroll = res.reduce((current, dt) => [...current, ...(dt.data || [])], []);
        setHasActivePayroll(Boolean(dataPayroll.length));
    };

    useEffect(() => {
        fetchOutletDetail(params.id);
        fetchPayroll(params.id);
    }, [JSON.stringify(params)]);

    const handleChangeCoachMark = ({ action }) => {
        const resetedAction = [ACTIONS.RESET];
        if (resetedAction.includes(action)) {
            // handle complete
            setShowCoachMark(false);
            setShowModalCoachMark(true);
        }
    };

    const handleCancelCoachMark = () => {
        // handle cancel coachmark
        setShowCoachMark(true);
        setShowModalCoachMark(false);
    };

    const handleConfirmCoachMark = () => {
        // handle confirm coachmark
        setShowModalCoachMark(false);
    };

    return (
        <React.Fragment>
            <PageDialog open={open} layer={1000} onOpenChange={handleCloseModal}>
                <PageDialog.Title>{t('title', 'Detail Outlet')}</PageDialog.Title>
                <PageDialog.Content wrapperSize="lg">
                    {showCoachMark && !usingSuppliesMaps && (
                        <CoachMarkOutlet t={t} steps={outletSteps} onChange={handleChangeCoachMark} />
                    )}
                    <Box as="form">
                        <FormOutletInformation
                            key={
                                showCoachMark
                                    ? 'info-section-with-coachmark'
                                    : `info-${outletDetail.id_cabang}-${get(outletDetail, 'supervisor.id_supervisor')}`
                            }
                            hookForm={hookForm}
                            outletDetail={outletDetail}
                            router={router}
                            hideProgress={hideProgress}
                            showProgress={showProgress}
                            t={t}
                        />
                    </Box>
                </PageDialog.Content>
                <PageDialog.Footer
                    css={{
                        justifyContent: 'center',
                        height: 'auto',
                        padding: '$spacing-05',
                        borderTop: '1px solid $gray150',
                        '@md': { height: 64, padding: '12px 24px', borderTop: 'none', marginRight: '$spacing-04' },
                    }}
                >
                    <Box
                        css={{
                            display: 'grid',
                            width: '100%',
                            justifyContent: 'center',
                            gridTemplateColumns: '1fr',
                            '@md': {
                                maxWidth: '1366px',
                                padding: '0 $spacing-06',
                                margin: 'auto',

                                // gridTemplateColumns: '311px 982px',
                            },
                        }}
                    >
                        <Box css={{ display: 'none', '@md': { display: 'block' } }} />
                        <Box css={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
                            {isMobile ? (
                                <IconButton
                                    onClick={() => {
                                        setOpenOption(true);
                                    }}
                                    css={{ alignSelf: 'center' }}
                                >
                                    <EllipsisHorizontalOutline color={colors.iconSecondary} />
                                </IconButton>
                            ) : (
                                <React.Fragment>
                                    {checkIsOwner() && (
                                        <TooltipDelete
                                            align="center"
                                            side="top"
                                            color="dark"
                                            label={t(
                                                'tooltip.delete',
                                                'Tidak dapat menghapus outlet karena transaksi penggajian masih berjalan di payroll',
                                            )}
                                            withClick={false}
                                            defaultOpen={false}
                                        >
                                            <Button
                                                onClick={() => setOpenDelete(true)}
                                                size="md"
                                                type="button"
                                                buttonType="negative-secondary"
                                                css={{ minWidth: 'unset', width: 40, padding: 0 }}
                                                disabled={hasActivePayroll}
                                            >
                                                <TrashOutline color="currentColor" />
                                            </Button>
                                        </TooltipDelete>
                                    )}
                                </React.Fragment>
                            )}
                            <Flex gap={5} css={{ flex: 1, '@md': { flex: 'unset' } }}>
                                <Button
                                    onClick={handleBackButton}
                                    size="md"
                                    buttonType="ghost"
                                    type="button"
                                    css={{ flex: 1, '@md': { flex: 'unset' } }}
                                >
                                    {t('label.cancel', { ns: 'translation', defaultValue: 'Batal' })}
                                </Button>
                                <Button
                                    onClick={handleNextButton}
                                    size="md"
                                    type="button"
                                    css={{ flex: 1, '@md': { flex: 'unset' } }}
                                >
                                    {t('label.save', { ns: 'translation', defaultValue: 'Simpan' })}
                                </Button>
                            </Flex>
                        </Box>
                    </Box>
                </PageDialog.Footer>
            </PageDialog>
            <ConfirmModal
                open={openConfirm}
                onOpenChange={setOpenConfirm}
                title={t('modal.save.title')}
                description={
                    <Trans t={t} i18nKey="modal.save.description">
                        {{ name: getValues('cabang_name') }}
                    </Trans>
                }
                onConfirm={handleUpdateOutlet}
            />
            <ConfirmModal
                negative
                open={openCancel}
                onOpenChange={setOpenCancel}
                title={t('modal.cancel.title')}
                description={t('modal.cancel.description')}
                onConfirm={handleCancel}
            />
            <ConfirmModal
                negative
                open={openDelete}
                onOpenChange={setOpenDelete}
                title={t('modal.delete.title')}
                description={
                    <Trans t={t} i18nKey="modal.delete.description">
                        {{ name: getValues('cabang_name') }}
                    </Trans>
                }
                onConfirm={handleDelete}
            />
            <WarningModal
                open={openWarning}
                onOpenChange={setOpenWarning}
                title={t('modal.undeleteable.title')}
                description={
                    <Trans t={t} i18nKey="modal.undeleteable.description">
                        {{ name: getValues('cabang_name') }}
                    </Trans>
                }
                t={t}
            />
            {checkIsOwner() && (
                <OptionModal
                    open={openOption}
                    onOpenChange={setOpenOption}
                    onDelete={() => setOpenDelete(true)}
                    t={t}
                />
            )}
            <PinModal open={openPin} onOpenChange={setOpenPin} onSubmit={handleSubmitPin} t={t} />
            <CoachMarkModal
                t={t}
                open={showModalCoachMark}
                onCancel={handleCancelCoachMark}
                onConfirm={handleConfirmCoachMark}
            />
            {/* Authorization Modal */}
            {openAuthorizationModal && (
                <AuthorizationModal
                    title={t('modal.delete.authorization', 'Otorisasi Hapus Outlet')}
                    isOpen={openAuthorizationModal}
                    onOpenChange={setOpenAuthorizationModal}
                    phoneNumber={userData.phone}
                    email={userData.email}
                    requestVerification={(contactValue, verificationType) => fetchRequestVerifCode(contactValue, verificationType)}
                    isMobile={isMobile}
                    t={t}
                    toOwner
                    outletId={filterBranch || idCabang}
                />
            )}

            {/* Verification Modal */}
            {verificationModal.open && (
                <VerificationModal
                    isMobile={isMobile}
                    isOpen={verificationModal.open}
                    onOpenChange={isOpen => setVerificationModal(current => ({ ...current, open: isOpen }))}
                    verificationType={verificationModal.type}
                    contactValue={verificationModal.contactValue || (verificationModal.type === 'email' ? userData.email : userData.phone)}
                    requestVerificationCode={async (verificationType, contactValue, onSuccess, onError) => {
                        await fetchRequestVerifCode(contactValue, verificationType, onSuccess, onError);
                    }}
                    onSubmit={(code, verificationType, onError) => fetchVerifyData(code, verificationType, onError)}
                    t={t}
                />
            )}
        </React.Fragment>
    );
};

BranchDetail.propTypes = {
    params: PropTypes.shape({
        id: PropTypes.string,
    }).isRequired,
    router: PropTypes.shape({
        replace: PropTypes.func,
        location: PropTypes.shape({
            query: PropTypes.shape({
                redirect: PropTypes.string,
            }),
        }),
    }),
    actions: PropTypes.shape({
        location: PropTypes.shape({
            getCity: PropTypes.func.isRequired,
        }),
    }).isRequired,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
    profile: PropTypes.shape({}),
};

BranchDetail.defaultProps = {
    router: {
        replace: () => {},
        location: {
            query: {},
        },
    },
    profile: {
        user_usaha_email: '',
        user_usaha_notlp: '',
    },
};

const mapStateToProps = state => ({
    cabang: state.branch.filter,
    profile: state.user.profile,
});

const mapDispatchToProps = dispatch => ({
    actions: {
        location: bindActionCreators(locationActionCreators, dispatch),
    },
    filterCabang: idCabang => {
        dispatch(filterCabang(idCabang));
    },
    fetchCabang: () => {
        dispatch(fetchCabang());
    },
});

export default connect(mapStateToProps, mapDispatchToProps)(CoreHOC(BranchDetail));
