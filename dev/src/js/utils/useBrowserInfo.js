import { useMemo } from 'react';

const useBrowserInfo = () => {
  // Memoize the browser info parsing since it never changes during a session
  const browserInfo = useMemo(() => {
    const parseUserAgent = () => {
      // Handle cases where navigator or userAgent might be null/undefined
      if (typeof navigator === 'undefined' || !navigator) {
        throw new Error('Navigator not available');
      }

      const ua = navigator.userAgent;
      
      // Handle null/undefined userAgent
      if (!ua || typeof ua !== 'string') {
        throw new Error('User agent not available');
      }

      let browserName = 'Unknown';
      let version = 'Unknown';
      let fullVersion = 'Unknown';

      // Chrome
      if (ua.includes('Chrome') && !ua.includes('Edg')) {
        browserName = 'Chrome';
        const match = ua.match(/Chrome\/(\d+)\.(\d+)\.(\d+)\.(\d+)/);
        if (match) {
          version = match[1];
          fullVersion = `${match[1]}.${match[2]}.${match[3]}.${match[4]}`;
        }
      }
      // Firefox
      else if (ua.includes('Firefox')) {
        browserName = 'Firefox';
        const match = ua.match(/Firefox\/(\d+)\.(\d+)/);
        if (match) {
          version = match[1];
          fullVersion = `${match[1]}.${match[2]}`;
        }
      }
      // Safari
      else if (ua.includes('Safari') && !ua.includes('Chrome')) {
        browserName = 'Safari';
        const match = ua.match(/Version\/(\d+)\.(\d+)/);
        if (match) {
          version = match[1];
          fullVersion = `${match[1]}.${match[2]}`;
        }
      }
      // Edge (Chromium-based)
      else if (ua.includes('Edg')) {
        browserName = 'Edge';
        const match = ua.match(/Edg\/(\d+)\.(\d+)\.(\d+)\.(\d+)/);
        if (match) {
          version = match[1];
          fullVersion = `${match[1]}.${match[2]}.${match[3]}.${match[4]}`;
        }
      }
      // Opera
      else if (ua.includes('OPR')) {
        browserName = 'Opera';
        const match = ua.match(/OPR\/(\d+)\.(\d+)/);
        if (match) {
          version = match[1];
          fullVersion = `${match[1]}.${match[2]}`;
        }
      }
      // Internet Explorer
      else if (ua.includes('MSIE') || ua.includes('Trident')) {
        browserName = 'Internet Explorer';
        const match = ua.match(/(?:MSIE |rv:)(\d+)\.(\d+)/);
        if (match) {
          version = match[1];
          fullVersion = `${match[1]}.${match[2]}`;
        }
      }

      return {
        name: browserName,
        version,
        fullVersion,
        userAgent: ua
      };
    };

    try {
      return parseUserAgent();
    } catch (error) {
      return {
        name: 'Unknown',
        version: 'Unknown',
        fullVersion: 'Unknown',
        userAgent: error.message || 'Error parsing user agent'
      };
    }
  }, []);

  return browserInfo;
};

export default useBrowserInfo;