{"emptyMessage": "No account yet", "bannerInfo": "Contact Hotline to change account number", "titleSection": {"title": "Bank Account Information", "add": "Add Bank Account", "settingSettlement": "Set Settlement Account", "authorization": "Bank Account Authorization"}, "header": {"name": "Bank Name", "no": "Account No", "owner": "Account Holder"}, "status": {"rejected": "Rejected", "verified": "Verified", "connected": "Connected", "pending": "Waiting for Verification"}, "info": {"rejected": "The photos attached do not match. Please upload the first page of the savings book or QR Code which contains the account number and account holder", "verified": "The bank account has been verified and can be used", "pending": "Bank account is in the verification process (max. 7 working days)", "connected": "Bank Raya has been connected"}, "f": {"title": {"new": "Create New Account", "add": "Add Bank Account", "edit": "Bank Account Details", "addRaya": "Add Raya Bank Account"}, "outlet": {"name": "Outlet List", "counter": "Selected Outlet", "required": "Please select an Outlet"}, "bankName": {"name": "Bank Name", "placeholder": "Example: Bank Jago", "required": "Please select a Bank Name"}, "accNumber": {"name": "Account Number", "placeholder": "Example: 1234567", "required": "Please fill in the Account Number"}, "accOwner": {"name": "Account Holder", "placeholder": "Example: <PERSON><PERSON>", "required": "Please fill in the Account Holder"}, "img": {"name": "Savings Book/QR Code Account Photos", "tooltip": "Savings Book: Use a photo of the first page of the savings book QR Code Account: Make sure the account number and account holder are printed on the QR Code", "required": "Please upload the first page of your savings book"}, "ktp": {"name": "KTP / NPWP / KITAS Account Holder", "required": "Please upload the KTP / NPWP / KITAS Account Holder"}, "verify": "Verify Account", "activate": "Activate Account", "forPayment": "Enable for Payroll", "activeLabel": "Activate to use Bank Account", "rayaDesc": "To verify an account, please enter your NIN and phone number registered to the account that will receive an SMS verification code", "rayaHelper": "*Make sure the number is active and has sufficient credit", "nik": {"name": "NIN (ID Card)", "placeholder": "Example: ****************"}, "phone": {"name": "Phone No", "placeholder": "Example: *********** or ************", "verify": "Send Verification Code", "loading": "Verification code is being sent via SMS", "inputOTP": "Enter the 6-digit verification code that has been sent to", "code": "Verification Code", "placeholderOTP": "Example: 123456", "invalidOTP": "Verification Code does not match", "verifyNow": "Verify Now", "notReceived": "Didn't get a code?", "resendOTP": "Resend", "success": "Succeed! Bank Raya Account has been connected"}}, "modal": {"cancel": {"titleAdd": "Cancel Add Bank Account", "titleEdit": "Cancel Edit Bank Account", "descAdd": "Cancelling <strong>Add Bank Account</strong> will delete all data that has been inputted and cannot be undone. Continue?", "descEdit": "Cancelling <strong>Edit Bank Account</strong> will delete all data that has been inputted and cannot be undone. Continue?"}, "confirm": {"titleAdd": "Add Bank Account", "titleEdit": "Save Bank Account", "descAdd": "The new bank account will be saved and appear in the bank account list according to the settings that have been made. Continue?", "descEdit": "Bank account changes will be saved and appear in the bank account list. Continue?"}, "delete": {"title": "Delete Bank Account", "desc": "Deleting <strong>{{name}}</strong> account will permanently remove the account and cannot be undone. Continue?"}, "failed": {"title": "Verification Failed", "agree": "Ok, Got it", "desc": "Account verification failed to process. Please make sure the following data is entered correctly and please re-verify:", "li1": "Account Number", "li2": "NIN (ID Card)", "li3": "Phone No"}, "info": {"title": "Verification Information", "agree": "Ok, Got it", "desc": "You have requested to resend the verification code 3 times. Please check:", "li1": "Mobile card active period", "li2": "Good phone signal quality", "li3": "For further assistance, contact the majoo Call Center at 1500 460", "desc2": "Account verification can be done again after 24 hours"}, "hasIntegerated": {"title": "Attention!", "agree": "Ok, Got it", "desc": "The Account Number has been linked to another device. Contact our support team for assistance"}}, "errors": {"ktp": "Please fill in the NIN (ID Card)", "phone": "Please fill in the Phone No", "invalidNumber": "Please make sure the Account Number is correct", "invalidKtp": "Please make sure the NIN (ID Card) is correct", "invalidPhone": "Please make sure the Phone Number is correct"}, "option": {"error": "Please select a Bank Name", "registered": "Registered Account", "detail": "Enter your bank account details", "new": "Create New Account", "newDesc": "Open a savings account at a bank that is integerated with majoo", "placeholder": "Select Bank Name", "title": "Add Bank account"}, "new": {"subTitle": "Here are the next steps you can take:", "li1": "After the account opening is successful, please link the account by clicking the <strong>Integerate Bank Account</strong> button below", "li2": "If you want to cancel the process of creating a new account, click the <strong>Cancel</strong> button (this action will delete all data that has been input)", "title": "Information", "confirm": "Integerate Bank Account", "cancel": "Cancel"}, "toast": {"success": "Bank Account successfully added", "error": "Bank Account failed to add", "deleteSuccess": "Bank Account successfully deleted", "deleteFailed": "Bank Account failed to delete", "successUpdate": "Bank Account successfully edited", "failedUpdate": "Bank Account failed to edit", "successStatus": "Bank Account successfully edited", "failedStatus": "Bank Account failed to edit", "settlementSuccess": "Settlement Account Successfully Added", "settlementError": "Settlement Account Failed to Added"}, "settlement": {"success": "Atur Rekening Pencairan Berhasil", "account": "Select Account", "accountDesc": "Select the account that will be used as a settlement account", "accountPlaceholder": "<PERSON><PERSON> Account", "outlet": "QRIS Settlement Outlets", "outletDesc": "Select the outlet that is registered on the account", "outletPlaceholder": "<PERSON><PERSON><PERSON>", "activate": "Online Store Settlements", "activateDesc": "Activate to change the online store settlement account to this account", "title": "Set Settlement Account", "form": {"settlementEwallet": "Set QRIS Settlement Account", "settlementEwalletDesc": "Activate to change QRIS settlement account to this account", "settlementOnlineStore": "Set Online Store Settlement Account"}}, "settlementAccount": {"status": {"onProcess": "Change In Process", "approved": "Change Approved", "rejected": "Change Rejected", "default": "Settlement Change"}, "statusDescription": {"onProcess": "Settlement account change request is being processed.", "approved": "Settlement account change has been approved.", "rejected": "The attached photo does not match. Please upload the first page of the savings book or QR Code that contains the account number and account holder name."}, "successMessage": "Settlement account settings changes have been saved successfully", "saveTitle": "Save Settlement Account", "saveDescription": "Settlement account settings will be saved and appear in the account list according to the settings made. Continue?", "uploadFailed": "Upload failed", "statementRequired": "Please upload the statement first", "dataSelected": "Data selected", "noAccountMatch": "No account matches the selected outlet criteria. Make sure the account is set up according to all selected outlets", "changeFile": "Change File", "detailTitle": "Settlement Account", "title": "Set Settlement Account", "processingInfo": "Settlement account setup request takes 2-3 working days", "settlementTypeLabel": "Settlement Type", "settlementTypeRequired": "Select at least one settlement type", "onlineStoreOption": "Online Store", "outletLabel": "Outlet", "outletRequired": "Select settlement destination outlet", "outletPlaceholder": "Select outlet", "accountLabel": "Account", "accountRequired": "Select settlement account", "accountPlaceholder": "Select settlement account", "statementLabel": "Statement", "uploadPlaceholder": "Select or drop files here", "noStatement": "Don't have a statement yet?", "downloadTemplate": "Download template", "ownerVerificationTitle": "Owner Account Verification", "processSettlementChange": "Settlement Change Process"}}