{"titleSection": {"title": "Store Settings"}, "bannerInfo": "Contact Hotline to change account number", "toggleActive": {"title": "Online Store Status", "published": "Published", "unpublished": "Unpublished"}, "selectOutlet": {"title": "Select Outlet"}, "selectTimezone": {"title": "Select Timezone"}, "timezone": {"wib": "Western Indonesian Time (WIB) — UTC+7", "wita": "Central Indonesian Time (WITA) — UTC+8", "wit": "Eastern Indonesian Time (WIT) — UTC+9"}, "loading": {"outletDetail": "Loading outlet data", "outletList": "Loading Outlet List...", "qrCode": "Loading QR Code...", "tableList": "Loading Floors and Tables..."}, "productPromoSetting": {"title": "Product & Promo Settings", "product": {"title": "Product", "subtitle": "Set the best products to be displayed on your online store", "button": "Set Product"}, "promo": {"title": "Promo", "subtitle": "Offer the best deals to increase sales and attract customers", "button": "Set Promo"}, "collection": {"title": "Collection", "subtitle": "Sort products in collections to make it easier for customers to browse products", "button": "Set Collection"}}, "serviceSetting": {"title": "Service Settings", "subtitle": "Select Service Type (Minimal 1", "description": "Activate the type of service you want to use to maximize your outlet transactions", "emenu": {"title": "<PERSON><PERSON><PERSON><PERSON>", "options": {"qrCode": "View details and export outlet's QR Code", "weblink": "Set and edit outlet's page link"}, "outlets": {"title": "All Outlets", "searchPlaceholder": "Search ...", "table": {"outletName": "OUTLET NAME", "action": "ACTION", "viewQR": "View QR"}, "notFound": {"title": "not found", "description": "Please try again with another keywords"}}, "export": {"inavailableQR": "QR Not Available", "printButton": "Print QR Code", "export": "Export"}}, "dineIn": {"title": "<PERSON>e In", "toggle": {"title": "<PERSON>e In", "description": "Enable it to make it easier for customers to order directly at your outlet"}, "printQROptions": {"title": "QR Code Print Options", "dynamic": {"title": "Print QR at Cashier", "description": "QR can be printed by the cashier via the Free Table & Table feature on the POS"}, "static": {"title": "Print QR per Table", "description": "QR will be printed and placed at each table manually"}}, "paymentOptions": {"title": "Payment Options", "payLater": {"title": "Pay Later", "description": "Orders can be processed before customers make payments"}, "directPayment": {"title": "Direct Payment", "description": "Orders can be processed after customers make payments"}}, "table": {"title": "Look for Floors and Tables", "setTable": "Set Table", "downloadAllQR": "Download All QR", "downloadingAllQR": "Downloading...", "viewQR": "View QR", "empty": {"description": "You have not set your table and floor yet. Immediately set the Table & Floor on the Floor Plan and Table page.", "button": "Go to Outlet Page"}}, "downloadPopup": {"title": "Download All QR", "closeButton": "Close"}, "takeAway": {"title": "Take Away", "tooltip": "This feature applies only to the\nOnline Store-Dine In/Take Away\nservice, when customers access it\nvia Cashier/Table QR", "helper": "Activate the Take Away option when customers order via Table/Cashier QR"}}, "takeAway": {"title": "Take Away", "description": "Optimize transactions by activating the takeaway order option"}, "delivery": {"title": "Delivery", "description": "Make it easy for customers to order anywhere through the delivery feature", "senderInfo": {"title": "Delivery Information", "info": "Make sure you have completed the address in the outlet settings.", "name": {"label": "Sender", "placeholder": "Input sender’s name"}, "phone": {"label": "Sender's Phone Number", "placeholder": "Input sender’s phone number"}}, "courierService": {"title": "Expedition Services", "description": "All expeditions support pickup services and Automatic AWB (Receipt). Make sure the selected expedition reaches your area.", "tosButton": "Read Terms and Conditions"}, "outletCourier": {"title": "Outlet Courier", "description": "By activating this feature, you can process the delivery directly with a private courier and determine your own delivery fees", "warning": "Internal Courier can only be used on the cashier / POS application version 2.29.3", "status": "Outlet Courier Status", "options": {"title": "Delivery Fee Based on", "weight": {"title": "Weight", "description": "The unit of weight used is Kilogram (Kg)", "form": {"title": "Weight Settings", "maxLabel": "Maximum Weight (Kg)", "maxPlaceholder": "Enter maximum weight", "feeLabel": "Delivery Fee (per Kg)"}}, "distance": {"title": "Distance/Location", "description": "The unit of distance used is Kilometer (Km)", "form": {"title": "Distance/Location Settings", "maxLabel": "Maximum Distance (Km)", "maxPlaceholder": "Enter maximum distance", "feeLabel": "Delivery Fee (per Km)"}}}, "estimation": {"label": "Estimated Time of Arrival", "to": "to"}}}}, "paymentSetting": {"title": "Payment Settings", "subtitle": "Select Payment Type", "description": "Adjust the payment types available at your outlet for easier payment process", "majooOrderDesc": "Receive orders through majoo Order and direct payment at the cashier. All transactions are automatically recorded in your store's dashboard.", "majooOrderWarning": "Extend your active period to continue using the majoo Order+ feature", "majooOrderButton": "Buy Add On", "qris": {"title": "QRIS Payment", "description": "A {{ mdr }}% fee are charged from the transaction"}, "virtualAccount": {"title": "Online Payment (Virtual Account)", "description": "Service fee of {{ amount }} is charged from the transaction.<br />(Can only be used on Take Away and Delivery Service)"}, "wallet": {"title": "Wallet Payment (Gopay, OVO, ShopeePay)", "description": "A {{ mdr }}% fee are charged from the transaction"}, "cashier": {"title": "Pay at Cashier", "description": "There are no transaction fees charged.<br />(Can only be used on Dine In Service - Pay Later)"}}, "accountNumber": {"title": "Select Account", "subtitle": "Set the transaction fund recipient, make sure the account belongs to owner/company", "formLabel": "Account Number", "formPlaceholder": "Tap to select one of your accounts", "unassignedAccountNumber": "Account for online shop transaction settlement process has not been added", "addAccountNumberButton": "Add Account", "bannerInfo": "You can change the online store settlement account via", "accountInfo": "account information page"}, "tooltip": {"copyLink": "Copy Link", "editLink": "Edit Link"}, "toast": {"copyLink": "Weblink successfully copied", "editLink": "successfully changed", "saveSetting": "<strong>Store Settings</strong> successfully saved", "premiumDomainInProgress": "Domain is still in validation process"}, "errors": {"optionCourier": "No courier options are available", "serviceSettingTitle": "Error Service Settings", "serviceSettingDescription": "You have not selected an order type, select at least 1 order type", "dineInTitle": "<PERSON><PERSON><PERSON>", "dineInType": "You have not selected a dine-in type", "dineInPaymentTime": "You have not selected a payment time", "courierServiceTitle": "Error Expedition Services", "courierServiceDescription": "You have not selected any expedition yet, please select at least 1 expedition", "paymentSettingTitle": "Error Payment Settings", "paymentSettingDescription": "You need to activate at least 1 type of payment for the online store", "outletCourierTitle": "<PERSON><PERSON>r Outlet Courier", "outletCourierMaxWeight": "Enter the maximum weight limit", "outletCourierMaxDistance": "Enter the maximum distance of delivery", "outletCourierEstimation": "Select the estimated order that arrives first", "outletCourierFailedEstimation": "Estimated order arrival cannot be higher than the second value"}, "modal": {"financialActivation": {"title": "Financial Account Activation", "description": "Please reactivate your financial account to integrate the online store with the accounting features in the majoo system.", "confirmButton": "Activate Financial Account", "confirmButtonLoading": "Activating Financial..."}, "promo": {"title": "Set Promo", "description": "Please select the type of promo that you want. You will be directed to the selected promo page.", "options": {"basicPromo": {"title": "Basic Promo", "description": "Discounts without time limits and can be deactivated at any time"}, "promoPerProduct": {"title": "Promo per Product", "description": "Discounted price for each product"}, "promoPerPurchase": {"title": "Promo per Total Purchase", "description": "Discounts based on total purchase"}, "coupon": {"title": "Coupon", "description": "Discounts with coupon codes for customers"}}}, "editWeblink": {"titleForm": "Edit Weblink", "titleConfirm": "Save Weblink", "description": "<strong>Weblink</strong> will be saved and appear in the online store menu according to the settings that have been made. Continue?", "savingButton": "Saving..."}, "qrCode": {"title": "QR Menu Details", "description": "This feature is used in table orders and is only available in advanced versions", "unavailableQR": "QR Not Available"}, "setTable": {"title": "Set Table", "description": "You will be directed to the Floor Plan and Table page to make table arrangements. Continue?"}, "printQRTable": {"title": "Online Order", "subtitle": "The outlet has been integrated with online order", "description": "Scan the barcode to place an online order", "confirmButton": "Download QR Code", "confirmButtonLoading": "Downloading..."}, "activeDelivery": {"title": "Activate Delivery Service", "description": "You will use the selected courier to deliver the product. Ensure that the weight and volume of the product are correct to avoid a discrepancy with the courier.", "confirmButton": "Activate Delivery Service"}, "setVolumeWeight": {"title": "Set Volume and Weight", "description": "Unable to activate <strong>Delivery</strong> service. Set the volume and weight of products displayed on the online store to activate the <strong>Delivery</strong> service.", "confirmButton": "Set Product"}, "nonactiveDelivery": {"title": "Deactivate Delivery Service", "description": "You will deactivate the <strong>Delivery</strong> service of the online shop. Come on?"}, "failedSetDelivery": {"title": "Set Delivery Address", "description": "Failed to save Service Settings <strong>Delivery</strong>. Set the address and location of the outlet for the product pickup process. Continue?"}, "saveSetting": {"title": "Save Settings", "description": "<strong>Store Settings</strong> will be saved and appear according to the settings that have been made. Continue?", "saving": "Saving..."}, "courierTOS": {"title": "Expedition Terms and Conditions", "description": "There are several terms and conditions for each expedition that can be used as a reference for delivery. Some things will be arranged on each expedition due to differences in the rules imposed by the expedition. These things will be arranged per expedition in the information below.", "jne": {"content": [{"title": "Service Type", "description": "Regular (REG), <PERSON><PERSON> (YES), <PERSON>g<PERSON> (OKE), Car<PERSON> (JTR)", "data": ["REG: 1-7 days Delivery (except Sundays and national holidays) depending on the delivery zone", "YES: 1 day (maximum received at 23.59 the next day, including Sundays and national holidays)", "OKE: 4-14 days Delivery (except Sundays and national holidays) depending on the delivery zone", "JTR: Minimum delivery of 10 kg by land road (non-air transport)"]}, {"title": "Request Seller Pick<PERSON>", "data": ["REG, OKE, JTR", "DKI : Max. at 09:59", "Non DKI : Max. at 11:59<br /><br />", "YES", "DKI : Max. at 09.59", "Non DKI : Max. at 11:59", "Estimated Pickup Time: 12:00-21:00"]}, {"title": "Items That Cannot be Shipped", "description": "JNE does not accept and has the right to refuse delivery of shipments that are prohibited under JNE terms and applicable laws in the Republic of Indonesia, including explosive or flammable dangerous goods, narcotics, psychotropics, firearms, sharp weapons, gold, stamps, stolen items, cheque, cash, traveller's cheque, objects that violate decency and/or other items which according to the law are declared prohibited items"}]}, "jnt": {"content": [{"title": "Service Type", "description": "<PERSON><PERSON><PERSON> (REG), <PERSON><PERSON> (YES), <PERSON>g<PERSON> (OKE), Car<PERSON> (JTR)", "data": ["REG: 1-7 days Delivery (except Sundays and national holidays) depending on the delivery zone", "YES: 1 day (maximum received at 23.59 the next day, including Sundays and national holidays)", "OKE: 4-14 days Delivery (except Sundays and national holidays) depending on the delivery zone", "JTR: Minimum delivery of 10 kg by land road (non-air transport)"]}, {"title": "Request Seller Pick<PERSON>", "data": ["REG, OKE, JTR", "DKI : Max. at 15.00", "NON DKI : Max. at 11.59", "Estimated Pickup Time: 15:00 - 22:00<br /><br />", "YES", "DKI : Max. at 09:59", "NON DKI : Max. at 11.59", "Estimated Pickup Time: 12.00 - 21.00"]}, {"title": "Items That Cannot be Shipped", "description": "J&T does not accept and has the right to refuse delivery of shipments that are prohibited under J&T terms and applicable laws in the Republic of Indonesia, including explosive or flammable dangerous goods, narcotics, psychotropics, firearms, sharp weapons, gold, stamps, stolen items, cheque, cash, traveller's cheque, objects that violate decency and/or other items which according to the law are declared prohibited items<br /><br />The sender releases J&T in the event of a loss and/or costs incurred including lawsuits, which result from the sender's negligence and mistakes arising from not complying with the terms in point 1.<br />J&T has the right to take the steps deemed necessary, immediately after learning of a violation of this point."}]}, "gosend": {"content": [{"title": "Service Type", "description": "Regular (REG), <PERSON><PERSON> (YES), <PERSON>g<PERSON> (OKE), Car<PERSON> (JTR)", "data": ["REG: 1-7 days Delivery (except Sundays and national holidays) depending on the delivery zone", "YES: 1 day (maximum received at 23.59 the next day, including Sundays and national holidays)", "OKE: 4-14 days Delivery (except Sundays and national holidays) depending on the delivery zone", "JTR: Minimum delivery of 10 kg by land road (non-air transport)"]}, {"title": "Request Seller Pick<PERSON>", "data": ["REG, OKE, JTR", "DKI : Max. at 09.59", "Non DKI : Max. at 11.59<br /><br />", "YES", "DKI : Max. at 09.59", "Non DKI : Max. at 11.59", "Estimated Pickup Time: 12.00-21.00"]}, {"title": "Items That Cannot be Shipped", "data": [{"title": "Prohibited items, including but not limited to:", "items": ["Money (cash, coins, foreign currency)", "Narcotics, cannabis, morphine, and other addictive products", "Pornography in any form", "Live animals and plants", "Perishable foodstuffs and beverages requiring refrigeration or a controlled environment", "Explosives, firearms, weapons, and their parts", "Gambling devices and lottery tickets", "Items controlled by the government", "Items resulting from crime, for example, stolen items and so on", "Other items prohibited by applicable laws and regulations, or by authorized officials or goods that require certain licenses or permits from authorized officials to be shipped."]}, {"title": "Unusual items, including but not limited to:", "items": ["Works of art, including works, made or done using skill, taste, or creative talent for sale, display, or collection, including, but not limited to items (and their parts) such as paintings, drawings, vases, rugs", "Films, photographic images, including photographic negatives, photographic chromes, photographic slides;", "Commodities that are naturally very vulnerable to damage, or market values that are very variable, or difficult to ascertain;", "Antiques, these commodities denote the style or fashion of a past era whose history, age, or rarity contribute to its value. These items include, but are not limited to, furniture, cutlery, glassware, and collectibles such as coins, stamps;", "Glassware in the form of jewelry, including costume jewelry, watches and parts thereof, gemstones or stones (precious or semi-precious) industrial diamonds, and jewelry made of precious metals;", "Animal fur;", "Precious metals, including but not limited to, gold and silver bars or powder, precipitate, or platinum (except as an integral part of electronic machinery)", "Infectious disease materials addressed to official laboratories or officials tasked with eradicating infectious diseases;", "Radioactive materials;", "Equipment for wrapping infectious disease materials that have been or have not been used sent between official laboratories;", "Stamps, excise on liquor, seals; and/or", "Gold coins (must be packed with coin headers or Safe-T Mailers and must be kept from touching one another or wrapped in a layered material)."]}, {"title": "Valuable documents, including but not limited to:", "items": ["Shipment with a secret classification that concerns the interests of the state;", "Freehold Title (SHM) and Building Rights Title (HGB);", "Vehicle Ownership Documents (BPKB), Graduation Certificates, Passport; and/or", "Bonds & Certificates of Deposit and other items defined by us as valuable documents.", "Items that exceed dimension restrictions"]}]}]}, "grabexpress": {"content": [{"title": "Service Type", "description": "Instant Delivery, Same Day Delivery", "data": ["Instant : 24 Hours", "Same Day : 09.00 - 14.00"]}, {"title": "Request Seller Pick<PERSON>", "data": ["Instant", "Pickup Time: Immediately", "Delivery Time: On the same day, generally 4 hours", "Same Day", "Pickup Time: 4 Hours", "Delivery Time: On the same day, generally 4 hours"]}, {"title": "Items That Cannot be Shipped", "data": [{"items": ["Items that are prohibited by applicable laws and regulations, or by authorized officials, or items that require certain licenses or permits from authorized officials to be shipped.", "Explosive and flammable dangerous items, illegal drugs, liquor, and/or goods which according to the authorities are prohibited from being produced and distributed;", "Live animals and plants, including but not limited to processed products of protected animals and plants, and parts of rare animals that have died/preserved;", "Illegal items include but are not limited to human organs, stolen items, gambling equipment and/or equipment, firearms, sharp weapons, and airsoft guns;", "Cash and letters or other valuable documents including but not limited to checks, postal letters, postcards, original marriage books, passports, original education certificates, tender documents, cheques, securities, bonds, shares, certificates, airplane tickets, B/L, L/C, credit card, and/or original vehicle ownership documents (BPKB);", "Printed materials, records, or other items that are contrary to moral values and can disrupt stability, security, and public order;", "Items that can endanger the courier during the Delivery process such as chemicals that are flammable or explosive, cause irritation and are toxic to the human body, chemical liquids, corrosive, toxic materials, and other dangerous items, or other objects that are classified as toxic hazardous materials (B3);", "Jewelry items and/or other valuables including but not limited to gold, silver, and diamonds;", "Items considered to have high historical value", "Corpse and/or ashes;", "Human organs;", "Waste; and", "Items that are prohibited from being traded"]}]}]}, "anteraja": {"content": [{"title": "Service Type", "description": "Regular (REG), <PERSON><PERSON> (YES), <PERSON>g<PERSON> (OKE), Car<PERSON> (JTR)", "data": ["REG: 1-7 days Delivery (except Sundays and national holidays) depending on the delivery zone", "YES: 1 day (maximum received at 23.59 the next day, including Sundays and national holidays)", "OKE: 4-14 days Delivery (except Sundays and national holidays) depending on the delivery zone", "JTR: Minimum delivery of 10 kg by land road (non-air transport)"]}, {"title": "Request Seller Pick<PERSON>", "data": ["REG, OKE, JTR", "DKI : Max. at 09.59", "Non DKI : Max. at 11.59<br /><br />", "YES", "DKI : Max. at 09.59", "Non DKI : Max. at 11.59", "Estimated Pickup Time: 12.00-21.00"]}, {"title": "Items That Cannot be Shipped", "description": "Anteraja does not accept and has the right to refuse delivery of shipments that are prohibited under Anteraja terms and applicable laws in the Republic of Indonesia, including explosive or flammable dangerous goods, narcotics, psychotropics, firearms, sharp weapons, gold, stamps, stolen items, cheque, cash, traveller's cheque, objects that violate decency and/or other items which according to the law are declared prohibited items"}]}, "ninja": {"content": [{"title": "Service Type", "description": "Reguler", "data": ["REG : 1 - 7 days delivery according to delivery destination"]}, {"title": "Request Seller Pick<PERSON>", "data": ["Seller Request Pickup", "00.00 - 08.59", "Estimated Pickup Time 09.00 - 12.00", "09.00 - 11.59", "Estimated Pickup Time 12.00 - 15.00", "12.00 - 14.59", "Estimated Pickup Time 15.00  - 18.00", "15.00 - 23.59", "Estimated Pickup Time Is The Next Day"]}, {"title": "Items That Cannot be Shipped", "description": "Ninja Express does not accept and has the right to refuse delivery of shipments that are prohibited under Ninja Express terms and applicable laws in the Republic of Indonesia, including explosive or flammable dangerous goods, narcotics, psychotropics, firearms, sharp weapons, gold, stamps, stolen items, cheque, cash, traveller's cheque, objects that violate decency and/or other items which according to the law are declared prohibited items<br /><br />The sender releases Ninja Express in the event of a loss and/or costs incurred including lawsuits, which result from the sender's negligence and mistakes arising from not complying with the terms in point 1.<br />Ninja Express has the right to take the steps deemed necessary, immediately after learning of a violation of this point."}]}, "sicepat": {"content": [{"title": "Service Type", "description": "Regular (REG), Tomorrow Until Destination (BEST), CARGO (GOKIL)", "data": ["REG: 1-7 days Delivery (except Sundays and national holidays) depending on the delivery zone", "BEST: 1 day (maximum received at 23.59 the next day, including Sundays and national holidays)", "CARGO: Minimum delivery of 10 kg by land road (non-air transport)"]}, {"title": "Request Seller Pick<PERSON>", "description": "REG, GOKIL", "data": ["Max at 17.59", "Estimated Pickup Time Is 10.00 - 20.00", "BEST", "Max at 14.59", "Estimated Pickup Time Is 10.00 - 18.00"]}, {"title": "Items That Cannot be Shipped", "description": "SiCepat does not accept and has the right to refuse delivery of shipments that are prohibited under SiCepat terms and applicable laws in the Republic of Indonesia, including explosive or flammable dangerous goods, narcotics, psychotropics, firearms, sharp weapons, gold, stamps, stolen items, cheque, cash, traveller's cheque, objects that violate decency and/or other items which according to the law are declared prohibited items<br /><br />The sender releases SiCepat in the event of a loss and/or costs incurred including lawsuits, which result from the sender's negligence and mistakes arising from not complying with the terms in point 1.<br />SiCepat has the right to take the steps deemed necessary, immediately after learning of a violation of this point."}]}, "tiki": {"content": [{"title": "Service Type", "description": "Reguler (REG), Overnight Services (ONS), Economy Services (ECO), Cargo (TRC)", "data": ["REG: 1-7 days Delivery (except Sundays and national holidays) depending on the delivery zone", "YES: 1 day (maximum received at 23.59 the next day, including Sundays and national holidays)", "OKE: 4-14 days Delivery (except Sundays and national holidays) depending on the delivery zone", "JTR: Minimum delivery of 10 kg by land road (non-air transport)"]}, {"title": "Request Seller Pick<PERSON>", "description": "REG, ECO, TRC", "data": ["REG, ECO, TRC", "DKI : Maksimal 15.00", "NON DKI : Maks<PERSON>l 11.59", "<PERSON><PERSON><PERSON><PERSON> 15.00 - 22.00", "ONS", "DKI : Maks 11.59", "NON DKI : Maks 09.59", "<PERSON><PERSON><PERSON><PERSON> 12.00 - 21.00"]}, {"title": "Items That Cannot be Shipped", "data": [{"title": "Prohibited Package Categories:", "items": ["Living things (animals and plants), money, securities (checks, bonds, stocks, certificates, etc.),", "Explosives, weapons and their parts, gambling equipment and lottery tickets, drugs and drugs or other prohibited items;", "Items that contravene the law, and moral values and can disrupt the stability of security and public order, items that are categorized under government supervision, goods made of glass, acrylic, and marble;", "Items in the category of dangerous, toxic, and explosive or flammable chemical goods, unless packed properly and correctly (by attaching a Material Safety Data Sheet and a statement letter for dangerous goods from the customer), alcohol, and alcoholic beverages."]}, {"title": "High-Value Package Category:", "items": ["Art goods, including works of expertise, talents or talents to be traded, exhibited, or even for collection, such as paintings, drawings, vases, and wall hangings made of tapestries;", "Antiques, are all types of commodities that show characteristics of the past and have their value because of their history, age, and rarity, such as furniture, cutlery, glassware, and collectibles such as coins and stamps;", "Jewelry, including jewelry made of non-genuine gems, watches and parts of these watches, genuine gems or gemstones (precious or semi-precious stones), craft diamonds (sharpened and shaped), and jewelry made of precious metal;", "Precious metals, including, inter alia, gold, and silver, platinum (except as an integral part of electronic equipment);", "Electronic products weighing up to 50 kg such as notebooks, digital cameras, and so on;", "Stamps, liquor excise stamps, tax stamps, and vouchers;", "High-value commodities, such as swallow's nests, animal fur, and silk."]}]}]}, "insurance": {"title": "Insurance", "content1": "Insurance for all expeditions is 0.5% of the total items", "content2": "An example of calculating insurance is if a customer checkouts goods with a nominal total, then the insurance is:<br />Total 100,000<br />Insurance 0.5%<br />Then, Insurance = Nominal x 0.5%<br />Insurance = 100,000 x 0.5%<br />Insurance = 500", "content3": "The maximum sum insured/Nominal that will be borne is Rp. 50.000.000,- for all expeditions", "content4": "Items that cannot be claimed include:", "data": ["Food", "Gold", "Noble Metal"]}}, "premiumDomainApply": {"title": "Premium Domain Applying Confirmation", "description": "Before applying, do you already have a Personal Domain?", "button": {"hasDomain": "Yes", "register": "Register Premium Domain"}}, "registerPremiumDomain": {"title": "Register Premium Domain", "tnc": {"title": "Premium Domain Terms & Conditions", "confirmTnC": {"title": "Confirm Terms & Conditions", "description": "By agreeing to majoo terms and conditions, Premium Domain Registration will be processed."}, "content": {"title": "This page regulates the Terms and Conditions where you use the Premium Domain Service.", "data": [{"title": "General", "children": ["These terms and conditions regulate the use of all premium domain services. This service is owned, operated and hosted by PT Majoo Technology Indonesia. By using the Premium Domain Service, you are considered to have given us the consent to use the information you provide for the purposes set out in the terms and conditions for applying for a premium domain.", "These Terms and Conditions can be changed, modified, added, deleted or corrected at any time and each of these changes is valid. <PERSON><PERSON> will provide information about any changes to the terms and conditions if there are any."]}, {"title": "Apply for Premium Domains", "children": ["When you apply for a premium domain, you agree to receive emails or calls from <PERSON><PERSON> in the form of information or confirmation of this premium domain application."]}, {"title": "Fee Payment", "children": ["The use of this premium domain will be charged outside of the subscription fee and there is a domain fee for each period according to the applicable regulations."]}], "caption": "I agree to the terms and conditions above"}}, "form": {"title": "Application Information", "mainOutlet": "Main Outlet Name", "majooEmail": "<PERSON><PERSON> Account <PERSON><PERSON>", "activeEmail": {"label": "Active Email", "placeholder": "Example: <EMAIL>", "sameEmail": "Same as majoo account email"}, "phoneNumber": {"label": "Phone Number", "placeholder": "Example: ************"}, "weblink": "Weblink", "domain": {"label": "Domain Name", "placeholder": "Example: barakopi.co.id"}, "nsStatus": {"label": "Nameserver Setup Status", "caption": "Have you changed Nameserver Setup", "description": "Learn How to Change/Setup Nameserver"}, "nsDate": {"label": "Nameserver Setup Date", "placeholder": "<PERSON>ose <PERSON>"}, "confirm": {"title": "Submit Premium Domain Application", "description": "Make sure the data that has been changed is correct. <PERSON><PERSON>'s team will contact your email soon, make sure your email is active. Continue?"}, "errors": {"main_outlet": "Main outlet name is required", "email_format": "Sorry, the email you entered does not match the format", "majoo_email": "<PERSON><PERSON> email is required", "active_email": "Active email is required", "weblink": "Weblink is required", "domain": "Domain is required", "phone_number": "Phone Number is required", "ns_status": "Please complete Nameserver status", "ns_setup_date": "Setup date is required"}, "setupInstuction": {"title": "How to Setup Nameserver", "caption": "To setup nameserver, please follow these steps:", "steps": ["Change nameserver in your domain settings in Nameserver section", "Enter nameserver 1 with bart.ns.cloudflare.com", "Enter nameserver 2 with venus.ns.cloudflare.com"], "callCenter": {"title": "Help Center 1500460 (Majoo Call Center)", "caption": "*Contact the help center if you experience problems while setting up the nameservers"}, "button": "Got It"}}}}, "premiumDomainSetting": {"title": "Premium Domain", "status": {"new": "Majoo offers premium domains to enhance your quality and business identity", "inProgress": {"title": "Validation Process", "description": "<PERSON><PERSON>'s team will contact you to validate the premium domain for your online store, namely"}, "failed": "Application Rejected!", "completed": "Online Store can also be accessed at"}, "button": "Apply Premium Domain"}}