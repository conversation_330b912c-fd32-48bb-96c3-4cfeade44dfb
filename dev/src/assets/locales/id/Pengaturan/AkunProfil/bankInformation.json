{"emptyMessage": "Belum ada rekening", "bannerInfo": "Hubungi Hotline untuk perubahan nomor rekening", "titleSection": {"title": "Informasi Rekening", "add": "Tambah Rekening Bank", "settingSettlement": "Atur Rekening Settlement", "authorization": "Otorisasi Rekening Bank"}, "header": {"name": "Nama Bank", "no": "No Rekening", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"rejected": "<PERSON><PERSON><PERSON>", "verified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON>"}, "info": {"rejected": "Foto yang dilampirkan tidak sesuai. <PERSON><PERSON>an unggah halaman pertama buku tabungan atau QR Code yang tertera nomor dan nama pemilik rekening.", "verified": "Rekening bank telah terverifikasi dan dapat digunakan", "pending": "Rekening bank sedang dalam proses veri<PERSON> (maks 7 hari kerja)", "connected": "Bank Raya telah terhubung"}, "f": {"title": {"new": "<PERSON><PERSON><PERSON>", "add": "Tambah Rekening Bank", "edit": "Detail Rekening Bank", "addRaya": "Tambah Rekening Bank Raya"}, "outlet": {"name": "Daftar Outlet", "counter": "Outlet Terpilih", "required": "<PERSON><PERSON>ng<PERSON>pi Outlet"}, "bankName": {"name": "Nama Bank", "placeholder": "Contoh: Bank Jago", "required": "Mohon lengkapi Nama Bank"}, "accNumber": {"name": "Nomor <PERSON>", "placeholder": "Contoh: 1234567", "required": "<PERSON><PERSON>"}, "accOwner": {"name": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Contoh: <PERSON><PERSON>", "required": "<PERSON><PERSON>"}, "img": {"name": "Foto Informasi Rekening", "tooltip": "Buku Tabungan: <PERSON><PERSON><PERSON> foto halaman pertama buku tabungan<br />QR Code Rekening: <PERSON><PERSON>n nomor rekening dan nama pemilik rekening tertera pada QR Code", "required": "Mohon lengkapi Foto Halaman Pertama Buku Tabungan"}, "ktp": {"name": "KTP / NPWP / KITAS Pemegang Rekening", "required": "Mohon lengkapi Foto KTP / NPWP / KITAS Pemegang Rekening"}, "verify": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activate": "Aktifkan Rekening", "forPayment": "<PERSON><PERSON><PERSON>", "activeLabel": "Aktifkan untuk menggunakan rekening", "rayaDesc": "Untuk memverifikasi rekening, silahkan masukkan NIK dan nomor ponsel yang terdaftar di rekening terkait yang akan menerima SMS kode verifikasi", "rayaHelper": "*Pastikan nomor aktif dan memiliki pulsa yang cukup", "nik": {"name": "NIK (KTP)", "placeholder": "Contoh: 3313031904330001"}, "phone": {"name": "No Ponsel", "placeholder": "Contoh: *********** atau ************", "verify": "<PERSON><PERSON>", "loading": "Kode verifikasi sedang dikirim melalui <PERSON>", "inputOTP": "Masukkan 6 digit kode verifikasi yang telah dikirimkan ke", "code": "<PERSON><PERSON>", "placeholderOTP": "Contoh: 123456", "invalidOTP": "Kode Verifikasi tidak sesuai", "verifyNow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notReceived": "Belum menerima kode verifikasi?", "resendOTP": "<PERSON><PERSON>", "success": "Berhasil! Rekening Bank Raya telah terhubung"}}, "modal": {"cancel": {"titleAdd": "Batal Tambah Rekening Bank", "titleEdit": "Batal Ubah Rekening Bank", "descAdd": "Membatalkan <strong>Tambah Rekening Bank</strong> akan menghapus seluruh data yang telah diinput dan tidak dapat dibatalkan. Lanjutkan?", "descEdit": "Membatalkan <strong>Ubah Rekening Bank</strong> akan menghapus seluruh perubahan data yang telah diinput dan tidak dapat dibatalkan. Lanjutkan?"}, "confirm": {"titleAdd": "Tambah Rekening Bank", "titleEdit": "Simpan Rekening Bank", "descAdd": "Rekening bank baru akan disimpan dan tampil di daftar rekening bank sesuai dengan pengaturan yang telah dilakukan. Lanjutkan?", "descEdit": "Perubahan rekening bank akan disimpan dan tampil di daftar rekening bank. Lanjutkan?"}, "delete": {"title": "Hapus Rekening Bank", "desc": "Menghapus rekening <strong>{{name}}</strong> akan menghilangkan rekening bank ini secara permanen dan tidak dapat dibatalkan. Lanjutkan?"}, "failed": {"title": "Verifi<PERSON><PERSON>", "agree": "<PERSON><PERSON>, <PERSON>ger<PERSON>", "desc": "Proses verifikasi rekening gagal dilakukan. Mohon pastikan data yang diinput berikut ini sesuai dan lakukan verifikasi ulang:", "li1": "Nomor <PERSON>", "li2": "NIK (KTP)", "li3": "No Ponsel"}, "info": {"title": "Informasi Verifikasi", "agree": "<PERSON><PERSON>, <PERSON>ger<PERSON>", "desc": "Anda sudah meminta kode verifikasi sebanyak 3 kali. Silahkan cek terlebih dahulu:", "li1": "Masa aktif kartu ponsel anda", "li2": "<PERSON><PERSON>n sinyal ponsel anda bagus", "li3": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>gubungi call center <PERSON><PERSON> di 0341-987-837", "desc2": "Atau dapat anda coba kirim kembali kode verifikasi setelah 1x24 jam"}, "hasIntegerated": {"title": "<PERSON><PERSON><PERSON>", "agree": "<PERSON><PERSON>, <PERSON>ger<PERSON>", "desc": "Nomor Rekening telah terhubung dengan perangkat lain. <PERSON><PERSON><PERSON><PERSON> tim support kami untuk mendapatkan bantuan"}}, "errors": {"ktp": "Mohon lengkapi NIK (KTP)", "phone": "<PERSON><PERSON> lengkapi No Ponsel", "invalidNumber": "<PERSON><PERSON> pastikan <PERSON> benar", "invalidKtp": "<PERSON><PERSON> pastikan NIK (KTP) benar", "invalidPhone": "<PERSON><PERSON> pastikan No Ponsel benar"}, "option": {"error": "Mohon pilih Nama Bank", "registered": "<PERSON><PERSON>", "detail": "Masukkan detail rekening bank yang Anda miliki", "new": "<PERSON><PERSON>", "newDesc": "Buka tabungan di bank yang terintegrasi dengan majoo", "placeholder": "Pilih Nama Bank", "title": "Tambah Rekening Bank"}, "new": {"subTitle": "<PERSON><PERSON><PERSON> langkah selanjutnya yang dapat Anda lakukan:", "li1": "<PERSON><PERSON><PERSON> pembu<PERSON><PERSON> rekening ber<PERSON><PERSON>, silakan hubungkan rekening dengan klik tombol <strong>Hubungkan Rekening Bank</strong> di bawah ini", "li2": "<PERSON>ka ingin memba<PERSON>kan proses pembuatan rekening baru, klik tombol <strong><PERSON><PERSON><PERSON></strong> (aksi ini akan menghapus seluruh data yang telah diinput)", "title": "Informasi", "confirm": "Hubungkan Rekening Bank", "cancel": "<PERSON><PERSON><PERSON>"}, "toast": {"success": "Rekening Bank berhasil ditambahkan", "error": "Rekening Bank gagal ditambahkan", "deleteSuccess": "Rekening Bank berhasil dihapus", "deleteFailed": "Rekening Bank gagal dihapus", "successUpdate": "Ubah Rekening Bank berhasil dilakukan", "failedUpdate": "Ubah Rekening Bank gagal dilakukan", "successStatus": "Ubah Rekening Bank berhasil dilakukan", "failedStatus": "Ubah Rekening Bank gagal dilakukan", "settlementSuccess": "Tambah Rekening Settlement Berhasil Dilakukan", "settlementError": "Tambah Rekening Settlement Gagal Dilakukan"}, "settlement": {"success": "Atur Rekening Pencairan Berhasil", "account": "<PERSON><PERSON><PERSON>", "accountDesc": "Pilih Rekening yang akan digunakan sebagai rekening settlement", "accountPlaceholder": "<PERSON><PERSON><PERSON>", "outlet": "Settlement Outlet QRIS", "outletDesc": "Pilih outlet yang terdaftar pada rekening", "outletPlaceholder": "<PERSON><PERSON><PERSON>", "activate": "Settlement Toko Online", "activateDesc": "Aktifkan untuk mengubah rekening settlement toko online ke rekening ini", "title": "Atur Rekening Settlement", "form": {"settlementEwallet": "Atur Rekening Settlement QRIS", "settlementEwalletDesc": "Pilih outlet untuk mengubah rekening settlement QRIS ke rekening ini", "settlementOnlineStore": "Atur Rekening Settlement Toko Online"}}, "settlementAccount": {"status": {"onProcess": "Perubahan Di<PERSON>roses", "approved": "<PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>"}, "statusDescription": {"onProcess": "Pengajuan perubahan rekening settlement sedang diproses.", "approved": "Perubahan rekening settlement telah disetujui.", "rejected": "Foto yang dilampirkan tidak sesuai. <PERSON><PERSON>an unggah halaman pertama buku tabungan atau QR Code yang tertera nomor dan nama pemilik rekening."}, "successMessage": "Perubahan pengaturan rekening settlement berhasil disimpan", "saveTitle": "Simpan Rekening Settlement", "saveDescription": "Pengaturan rekening settlement akan disimpan dan tampil di daftar rekening sesuai dengan pengaturan yang dilakukan. Lanjutkan?", "uploadFailed": "<PERSON><PERSON><PERSON> gagal", "statementRequired": "Unggah surat pernyataan terlebih dahulu", "dataSelected": "Data dipilih", "noAccountMatch": "Tidak ada rekening sesuai dengan kriteria outlet dipilih. Pastikan rekening yang diatur sesuai semua Outlet dipilih", "changeFile": "Ubah Berkas", "detailTitle": "Rekening Settlement", "title": "Atur Rekening Settlement", "processingInfo": "Pengajuan atur rekening settlement membutuhkan waktu 2-3 hari kerja", "settlementTypeLabel": "Jenis Settlement", "settlementTypeRequired": "Pilih minimal satu jenis settlement", "onlineStoreOption": "Toko Online", "outletLabel": "Outlet", "outletRequired": "Pilih outlet tujuan settlement", "outletPlaceholder": "Pilih outlet", "accountLabel": "Rekening", "accountRequired": "Pilih rekening settlement", "accountPlaceholder": "Pilih rekening settlement", "statementLabel": "Surat Pernyataan", "uploadPlaceholder": "<PERSON><PERSON><PERSON> atau letakkan berkas di sini", "noStatement": "Belum memiliki surat pernyataan?", "downloadTemplate": "Unduh template", "ownerVerificationTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> Owner"}}